*********
Structure
*********

Tree
====

::

  .
  ├── docs
  ├── examples
  ├── hw
  │   ├── bsp
  │   └── mcu
  ├── lib
  ├── src
  ├── test
  └── tools

docs
----

Documentation

examples
--------

Sample with Makefile build support

hw/bsp
------

Supported boards source files

hw/mcu
------

Low level mcu core & peripheral drivers

lib
---

Sources from 3rd party such as freeRTOS, fatfs ...

src
---

All sources files for TinyUSB stack itself.

test
----

Unit tests for the stack

tools
-----

Files used internally

@echo off
REM 测试auto关键字修正 - 验证C语言类型声明问题是否已解决

echo ========================================
echo 测试C语言类型声明修正
echo ========================================

REM 检查是否提供了板子名称参数
if "%1"=="" (
    echo 使用默认板子: feather_nrf52840_express
    set BOARD_NAME=feather_nrf52840_express
) else (
    set BOARD_NAME=%1
)

echo 目标板子: %BOARD_NAME%
echo.

echo [1/2] 清理之前的编译...
make BOARD=%BOARD_NAME% clean > nul 2>&1

echo [2/2] 测试编译main.c...
echo 正在编译main.c...

REM 尝试编译main.c来测试类型声明修正
make BOARD=%BOARD_NAME% DEBUG=1 _build/build-%BOARD_NAME%/src/main.o
if errorlevel 1 (
    echo.
    echo ❌ main.c 编译失败! 可能还有其他问题
    echo.
    echo 常见问题:
    echo 1. 类型声明错误
    echo 2. 头文件包含问题
    echo 3. 宏定义错误
    echo.
    echo 请检查具体的编译错误信息
    goto :end
) else (
    echo ✅ main.c 编译成功!
)

echo.
echo ========================================
echo ✅ 类型声明问题已解决!
echo ========================================
echo.
echo 修正内容:
echo   - 将 'auto mode' 改为 'int mode'
echo   - 将 'auto buflen' 改为 'int buflen'
echo   - 添加了 #ifdef CFG_DEBUG 保护
echo   - 使用正确的C语言语法
echo.
echo 现在可以进行完整编译:
echo   make BOARD=%BOARD_NAME% DEBUG=1 all
echo.
echo 或者直接烧录:
echo   make BOARD=%BOARD_NAME% DEBUG=1 all
echo   make BOARD=%BOARD_NAME% flash

:end
pause

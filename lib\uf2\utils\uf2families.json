[{"id": "0x16573617", "short_name": "ATMEGA32", "description": "Microchip (Atmel) ATmega32"}, {"id": "0x1851780a", "short_name": "SAML21", "description": "Microchip (Atmel) SAML21"}, {"id": "0x1b57745f", "short_name": "NRF52", "description": "Nordic NRF52"}, {"id": "0x1c5f21b0", "short_name": "ESP32", "description": "ESP32"}, {"id": "0x1e1f432d", "short_name": "STM32L1", "description": "ST STM32L1xx"}, {"id": "0x202e3a91", "short_name": "STM32L0", "description": "ST STM32L0xx"}, {"id": "0x21460ff0", "short_name": "STM32WL", "description": "ST STM32WLxx"}, {"id": "0x22e0d6fc", "short_name": "RTL8710B", "description": "Realtek AmebaZ RTL8710B"}, {"id": "0x2abc77ec", "short_name": "LPC55", "description": "NXP LPC55xx"}, {"id": "0x300f5633", "short_name": "STM32G0", "description": "ST STM32G0xx"}, {"id": "0x31d228c6", "short_name": "GD32F350", "description": "GD32F350"}, {"id": "0x3379CFE2", "short_name": "RTL8720D", "description": "Realtek AmebaD RTL8720D"}, {"id": "0x04240bdf", "short_name": "STM32L5", "description": "ST STM32L5xx"}, {"id": "0x4c71240a", "short_name": "STM32G4", "description": "ST STM32G4xx"}, {"id": "0x4fb2d5bd", "short_name": "MIMXRT10XX", "description": "NXP i.MX RT10XX"}, {"id": "0x51e903a8", "short_name": "XR809", "description": "Xradiotech 809"}, {"id": "0x53b80f00", "short_name": "STM32F7", "description": "ST STM32F7xx"}, {"id": "0x55114460", "short_name": "SAMD51", "description": "Microchip (Atmel) SAMD51"}, {"id": "0x57755a57", "short_name": "STM32F4", "description": "ST STM32F4xx"}, {"id": "0x5a18069b", "short_name": "FX2", "description": "Cypress FX2"}, {"id": "0x5d1a0a2e", "short_name": "STM32F2", "description": "ST STM32F2xx"}, {"id": "0x5ee21072", "short_name": "STM32F1", "description": "ST STM32F103"}, {"id": "0x621e937a", "short_name": "NRF52833", "description": "Nordic NRF52833"}, {"id": "0x647824b6", "short_name": "STM32F0", "description": "ST STM32F0xx"}, {"id": "0x675a40b0", "short_name": "BK7231U", "description": "Beken 7231U/7231T"}, {"id": "0x68ed2b88", "short_name": "SAMD21", "description": "Microchip (Atmel) SAMD21"}, {"id": "0x6a82cc42", "short_name": "BK7251", "description": "Beken 7251/7252"}, {"id": "0x6b846188", "short_name": "STM32F3", "description": "ST STM32F3xx"}, {"id": "0x6d0922fa", "short_name": "STM32F407", "description": "ST STM32F407"}, {"id": "0x4e8f1c5d", "short_name": "STM32H5", "description": "ST STM32H5xx"}, {"id": "0x6db66082", "short_name": "STM32H7", "description": "ST STM32H7xx"}, {"id": "0x70d16653", "short_name": "STM32WB", "description": "ST STM32WBxx"}, {"id": "0x7b3ef230", "short_name": "BK7231N", "description": "Beken 7231N"}, {"id": "0x7eab61ed", "short_name": "ESP8266", "description": "ESP8266"}, {"id": "0x7f83e793", "short_name": "KL32L2", "description": "NXP KL32L2x"}, {"id": "0x8fb060fe", "short_name": "STM32F407VG", "description": "ST STM32F407VG"}, {"id": "0x9fffd543", "short_name": "RTL8710A", "description": "Realtek Ameba1 RTL8710A"}, {"id": "0xada52840", "short_name": "NRF52840", "description": "Nordic NRF52840"}, {"id": "0x820d9a5f", "short_name": "NRF52820", "description": "Nordic NRF52820_xxAA"}, {"id": "0xbfdd4eee", "short_name": "ESP32S2", "description": "ESP32-S2"}, {"id": "0xc47e5767", "short_name": "ESP32S3", "description": "ESP32-S3"}, {"id": "0xd42ba06c", "short_name": "ESP32C3", "description": "ESP32-C3"}, {"id": "0x2b88d29c", "short_name": "ESP32C2", "description": "ESP32-C2"}, {"id": "0x332726f6", "short_name": "ESP32H2", "description": "ESP32-H2"}, {"id": "0x540ddf62", "short_name": "ESP32C6", "description": "ESP32-C6"}, {"id": "0x3d308e94", "short_name": "ESP32P4", "description": "ESP32-P4"}, {"id": "0xf71c0343", "short_name": "ESP32C5", "description": "ESP32-C5"}, {"id": "0x77d850c4", "short_name": "ESP32C61", "description": "ESP32-C61"}, {"id": "0xb6dd00af", "short_name": "ESP32H21", "description": "ESP32-H21"}, {"id": "0x9e0baa8a", "short_name": "ESP32H4", "description": "ESP32-H4"}, {"id": "0xde1270b7", "short_name": "BL602", "description": "Bo<PERSON>allo 602"}, {"id": "0xe08f7564", "short_name": "RTL8720C", "description": "Realtek AmebaZ2 RTL8720C"}, {"id": "0xe48bff56", "short_name": "RP2040", "description": "Raspberry Pi RP2040"}, {"id": "0xe48bff57", "short_name": "RP2XXX_ABSOLUTE", "description": "Raspberry Pi Microcontrollers: Absolute (unpartitioned) download"}, {"id": "0xe48bff58", "short_name": "RP2XXX_DATA", "description": "Raspberry Pi Microcontrollers: Data partition download"}, {"id": "0xe48bff59", "short_name": "RP2350_ARM_S", "description": "Raspberry Pi RP2350, Secure Arm image"}, {"id": "0xe48bff5a", "short_name": "RP2350_RISCV", "description": "Raspberry Pi RP2350, RISC-V image"}, {"id": "0xe48bff5b", "short_name": "RP2350_ARM_NS", "description": "Raspberry Pi RP2350, Non-secure Arm image"}, {"id": "0x00ff6919", "short_name": "STM32L4", "description": "ST STM32L4xx"}, {"id": "0x9af03e33", "short_name": "GD32VF103", "description": "GigaDevice GD32VF103"}, {"id": "0x4f6ace52", "short_name": "CSK4", "description": "LISTENAI CSK300x/400x"}, {"id": "0x6e7348a8", "short_name": "CSK6", "description": "LISTENAI CSK60xx"}, {"id": "0x11de784a", "short_name": "M0SENSE", "description": "M0SENSE BL702"}, {"id": "0x4b684d71", "short_name": "MaixPlay-U4", "description": "Sipeed MaixPlay-U4(BL618)"}, {"id": "0x9517422f", "short_name": "RZA1LU", "description": "Renesas RZ/A1LU (R7S7210xx)"}, {"id": "0x2dc309c5", "short_name": "STM32F411xE", "description": "ST STM32F411xE"}, {"id": "0x06d1097b", "short_name": "STM32F411xC", "description": "ST STM32F411xC"}, {"id": "0x72721d4e", "short_name": "NRF52832xxAA", "description": "Nordic NRF52832xxAA"}, {"id": "0x6f752678", "short_name": "NRF52832xxAB", "description": "Nordic NRF52832xxAB"}, {"id": "0xa0c97b8e", "short_name": "AT32F415", "description": "ArteryTek AT32F415"}, {"id": "0x699b62ec", "short_name": "CH32V", "description": "WCH CH32V2xx and CH32V3xx"}, {"id": "0x7be8976d", "short_name": "RA4M1", "description": "Renesas RA4M1"}, {"id": "0x7410520a", "short_name": "MAX32690", "description": "Analog Devices MAX32690"}, {"id": "0xd63f8632", "short_name": "MAX32650", "description": "Analog Devices MAX32650/1/2"}, {"id": "0xf0c30d71", "short_name": "MAX32666", "description": "Analog Devices MAX32665/6"}, {"id": "0x91d3fd18", "short_name": "MAX78002", "description": "Analog Devices MAX78002"}]
@echo off
REM 快速编译测试 - 验证头文件问题是否已解决

echo ========================================
echo 快速编译测试 - 验证RTT头文件修正
echo ========================================

REM 检查是否提供了板子名称参数
if "%1"=="" (
    echo 使用默认板子: feather_nrf52840_express
    set BOARD_NAME=feather_nrf52840_express
) else (
    set BOARD_NAME=%1
)

echo 目标板子: %BOARD_NAME%
echo.

echo [1/2] 清理之前的编译...
make BOARD=%BOARD_NAME% clean > nul 2>&1

echo [2/2] 测试DEBUG模式编译 (只编译几个文件)...
echo 正在编译...

REM 尝试编译main.c来测试头文件
make BOARD=%BOARD_NAME% DEBUG=1 _build/build-%BOARD_NAME%/src/main.o
if errorlevel 1 (
    echo.
    echo ❌ 编译失败! 头文件问题仍然存在
    echo.
    echo 可能的问题:
    echo 1. SEGGER_RTT.h 路径不正确
    echo 2. 缺少必要的包含路径
    echo 3. 编译器找不到RTT库文件
    echo.
    echo 请检查:
    echo - src/boards/boards.h 中的 #include "SEGGER_RTT.h"
    echo - src/main.c 中的 #include "SEGGER_RTT.h"
    echo - Makefile 中的 IPATH += $(RTT_SRC)/RTT
    goto :end
) else (
    echo.
    echo ✅ 编译成功! 头文件问题已解决
    echo.
    echo 现在可以进行完整编译:
    echo   make BOARD=%BOARD_NAME% DEBUG=1 all
    echo.
    echo 或者烧录到设备:
    echo   make BOARD=%BOARD_NAME% DEBUG=1 all
    echo   make BOARD=%BOARD_NAME% flash
)

:end
pause

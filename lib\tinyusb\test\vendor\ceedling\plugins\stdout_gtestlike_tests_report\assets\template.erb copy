% ignored        = hash[:results][:counts][:ignored]
% failed         = hash[:results][:counts][:failed]
% stdout_count   = hash[:results][:counts][:stdout]
% header_prepend = ((hash[:header].length > 0) ? "#{hash[:header]}: " : '')
% banner_width   = 25 + header_prepend.length # widest message


% if (stdout_count > 0)
[==========] Running <%=hash[:results][:counts][:total].to_s%> tests from <%=hash[:results][:stdout].length.to_s%> test cases.
[----------] Global test environment set-up.
% end
% if (failed > 0)
%   hash[:results][:failures].each do |failure|
[----------] <%=failure[:collection].length.to_s%> tests from <%=failure[:source][:file]%>
%     failure[:collection].each do |item|
[ RUN      ] <%=failure[:source][:file]%>.<%=item[:test]%>
% if (not item[:message].empty?)
<%=failure[:source][:file]%>(<%=item[:line]%>): error: <%=item[:message]%>

%   m = item[:message].match(/Expected\s+(.*)\s+Was\s+([^\.]*)\./)
%    if m.nil?
 Actual:   FALSE
 Expected: TRUE
%    else
 Actual:   <%=m[2]%>
 Expected: <%=m[1]%>
%    end
% else
<%=failure[:source][:file]%>(<%=item[:line]%>): fail: <%=item[:message]%>
 Actual:   FALSE
 Expected: TRUE
% end
[  FAILED  ] <%=failure[:source][:file]%>.<%=item[:test]%> (0 ms)
%     end
[----------] <%=failure[:collection].length.to_s%> tests from <%=failure[:source][:file]%> (0 ms total)
%   end
% end
% if (hash[:results][:counts][:total] > 0)
[----------] Global test environment tear-down.
[==========] <%=hash[:results][:counts][:total].to_s%> tests from <%=hash[:results][:stdout].length.to_s%> test cases ran.
[  PASSED  ] <%=hash[:results][:counts][:passed].to_s%> tests.
% if (failed == 0)
[  FAILED  ] 0 tests.

 0 FAILED TESTS
% else
[  FAILED  ] <%=failed.to_s%> tests, listed below:
%   hash[:results][:failures].each do |failure|
%     failure[:collection].each do |item|
[  FAILED  ] <%=failure[:source][:file]%>.<%=item[:test]%>
%     end
%   end

 <%=failed.to_s%> FAILED TESTS
% end
% else

No tests executed.
% end

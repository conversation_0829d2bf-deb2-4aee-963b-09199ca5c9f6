<?xml version='1.0' encoding='UTF-8' standalone='no'?>
<doxygen xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="compound.xsd" version="1.8.15">
  <compounddef id="struct_x_m_c___v_a_d_c___g_l_o_b_a_l___c_l_a_s_s__t" kind="struct" language="C++" prot="public">
    <compoundname>XMC_VADC_GLOBAL_CLASS_t</compoundname>
    <includes local="no">xmc_vadc.h</includes>
      <sectiondef kind="public-attrib">
      <memberdef kind="variable" id="struct_x_m_c___v_a_d_c___g_l_o_b_a_l___c_l_a_s_s__t_1a032017cf69509f2401cc4d7e849e793f" prot="public" static="no" mutable="no">
        <type>union XMC_VADC_GLOBAL_CLASS_t::@185</type>
        <definition>union XMC_VADC_GLOBAL_CLASS_t::@185 @186</definition>
        <argsstring></argsstring>
        <name>@186</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" line="1056" column="1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___v_a_d_c___g_l_o_b_a_l___c_l_a_s_s__t_1a3e57c2ef1c3ffb36722f000cc1156824" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t __pad0__</definition>
        <argsstring></argsstring>
        <name>__pad0__</name>
        <bitfield> 3</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" line="1040" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" bodystart="1040" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___v_a_d_c___g_l_o_b_a_l___c_l_a_s_s__t_1a6712ba6dd1d5b43d2d56ff8ac4e275a7" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t __pad1__</definition>
        <argsstring></argsstring>
        <name>__pad1__</name>
        <bitfield> 5</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" line="1043" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" bodystart="1043" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___v_a_d_c___g_l_o_b_a_l___c_l_a_s_s__t_1a9ce12a63de64ef64ae2d59d128251cae" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t __pad2__</definition>
        <argsstring></argsstring>
        <name>__pad2__</name>
        <bitfield> 3</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" line="1047" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" bodystart="1047" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___v_a_d_c___g_l_o_b_a_l___c_l_a_s_s__t_1a4854608c0e776f0704a4d9a4b98ea57d" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t __pad3__</definition>
        <argsstring></argsstring>
        <name>__pad3__</name>
        <bitfield> 5</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" line="1050" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" bodystart="1050" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___v_a_d_c___g_l_o_b_a_l___c_l_a_s_s__t_1a01eeeee4da2b1422e19d7fd892051d0d" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t conversion_mode_emux</definition>
        <argsstring></argsstring>
        <name>conversion_mode_emux</name>
        <bitfield> 3</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>Conversion mode for channels connected via EMUX to VADC. Uses <ref refid="group___v_a_d_c_1ga9f7e6983d71750230e15684a2dc0cf12" kindref="member">XMC_VADC_CONVMODE_t</ref> </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" line="1048" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" bodystart="1048" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___v_a_d_c___g_l_o_b_a_l___c_l_a_s_s__t_1a85d7d0d259905d2055aea8c60d40322e" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t conversion_mode_standard</definition>
        <argsstring></argsstring>
        <name>conversion_mode_standard</name>
        <bitfield> 3</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>Conversion mode for channels directly connected to VADC. Uses <ref refid="group___v_a_d_c_1ga9f7e6983d71750230e15684a2dc0cf12" kindref="member">XMC_VADC_CONVMODE_t</ref> </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" line="1041" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" bodystart="1041" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___v_a_d_c___g_l_o_b_a_l___c_l_a_s_s__t_1a42933181770ed7b5f1540c210d8a6bb6" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t globiclass</definition>
        <argsstring></argsstring>
        <name>globiclass</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" line="1055" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" bodystart="1055" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___v_a_d_c___g_l_o_b_a_l___c_l_a_s_s__t_1a1c640c0b3e426866078c9c727636c3b8" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t sample_time_std_conv</definition>
        <argsstring></argsstring>
        <name>sample_time_std_conv</name>
        <bitfield> 5</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>Sample time for channels directly connected to VADC <linebreak/>
Range: [0x0 to 0x1F] </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" line="1038" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" bodystart="1038" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___v_a_d_c___g_l_o_b_a_l___c_l_a_s_s__t_1a7e20447b6f896dd67c7a8424fd376fb1" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t sampling_phase_emux_channel</definition>
        <argsstring></argsstring>
        <name>sampling_phase_emux_channel</name>
        <bitfield> 5</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>Sample time for channels connected via EMUX <linebreak/>
Range: [0x0 to 0x1F] </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" line="1045" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" bodystart="1045" bodyend="-1"/>
      </memberdef>
      </sectiondef>
    <briefdescription>
    </briefdescription>
    <detaileddescription>
<para>Structure to initialize the global input class configuration. Configured parameters are sample time and conversion Mode. </para>
    </detaileddescription>
    <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" line="1033" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" bodystart="1032" bodyend="1057"/>
    <listofallmembers>
      <member refid="struct_x_m_c___v_a_d_c___g_l_o_b_a_l___c_l_a_s_s__t_1a3e57c2ef1c3ffb36722f000cc1156824" prot="public" virt="non-virtual"><scope>XMC_VADC_GLOBAL_CLASS_t</scope><name>__pad0__</name></member>
      <member refid="struct_x_m_c___v_a_d_c___g_l_o_b_a_l___c_l_a_s_s__t_1a6712ba6dd1d5b43d2d56ff8ac4e275a7" prot="public" virt="non-virtual"><scope>XMC_VADC_GLOBAL_CLASS_t</scope><name>__pad1__</name></member>
      <member refid="struct_x_m_c___v_a_d_c___g_l_o_b_a_l___c_l_a_s_s__t_1a9ce12a63de64ef64ae2d59d128251cae" prot="public" virt="non-virtual"><scope>XMC_VADC_GLOBAL_CLASS_t</scope><name>__pad2__</name></member>
      <member refid="struct_x_m_c___v_a_d_c___g_l_o_b_a_l___c_l_a_s_s__t_1a4854608c0e776f0704a4d9a4b98ea57d" prot="public" virt="non-virtual"><scope>XMC_VADC_GLOBAL_CLASS_t</scope><name>__pad3__</name></member>
      <member refid="struct_x_m_c___v_a_d_c___g_l_o_b_a_l___c_l_a_s_s__t_1a01eeeee4da2b1422e19d7fd892051d0d" prot="public" virt="non-virtual"><scope>XMC_VADC_GLOBAL_CLASS_t</scope><name>conversion_mode_emux</name></member>
      <member refid="struct_x_m_c___v_a_d_c___g_l_o_b_a_l___c_l_a_s_s__t_1a85d7d0d259905d2055aea8c60d40322e" prot="public" virt="non-virtual"><scope>XMC_VADC_GLOBAL_CLASS_t</scope><name>conversion_mode_standard</name></member>
      <member refid="struct_x_m_c___v_a_d_c___g_l_o_b_a_l___c_l_a_s_s__t_1a42933181770ed7b5f1540c210d8a6bb6" prot="public" virt="non-virtual"><scope>XMC_VADC_GLOBAL_CLASS_t</scope><name>globiclass</name></member>
      <member refid="struct_x_m_c___v_a_d_c___g_l_o_b_a_l___c_l_a_s_s__t_1a1c640c0b3e426866078c9c727636c3b8" prot="public" virt="non-virtual"><scope>XMC_VADC_GLOBAL_CLASS_t</scope><name>sample_time_std_conv</name></member>
      <member refid="struct_x_m_c___v_a_d_c___g_l_o_b_a_l___c_l_a_s_s__t_1a7e20447b6f896dd67c7a8424fd376fb1" prot="public" virt="non-virtual"><scope>XMC_VADC_GLOBAL_CLASS_t</scope><name>sampling_phase_emux_channel</name></member>
    </listofallmembers>
  </compounddef>
</doxygen>

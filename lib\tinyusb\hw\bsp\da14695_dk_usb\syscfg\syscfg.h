/**
 * This file was generated by Apache newt version: 1.9.0-dev
 */

#ifndef H_MYNEWT_SYSCFG_
#define H_MYNEWT_SYSCFG_

/**
 * This macro exists to ensure code includes this header when needed.  If code
 * checks the existence of a setting directly via ifdef without including this
 * header, the setting macro will silently evaluate to 0.  In contrast, an
 * attempt to use these macros without including this header will result in a
 * compiler error.
 */
#define MYNEWT_VAL(_name)                       MYNEWT_VAL_ ## _name
#define MYNEWT_VAL_CHOICE(_name, _val)          MYNEWT_VAL_ ## _name ## __ ## _val

#ifndef MYNEWT_VAL_RAM_RESIDENT
#define MYNEWT_VAL_RAM_RESIDENT (0)
#endif

#ifndef MYNEWT_VAL_MCU_GPIO_MAX_IRQ
#define MYNEWT_VAL_MCU_GPIO_MAX_IRQ (4)
#endif

#ifndef MYNEWT_VAL_MCU_GPIO_RETAINABLE_NUM
#define MYNEWT_VAL_MCU_GPIO_RETAINABLE_NUM (-1)
#endif

#ifndef MYNEWT_VAL_MCU_CLOCK_XTAL32M_SETTLE_TIME_US
#define MYNEWT_VAL_MCU_CLOCK_XTAL32M_SETTLE_TIME_US (2000)
#endif

#endif

<?xml version='1.0' encoding='UTF-8' standalone='no'?>
<doxygen xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="compound.xsd" version="1.8.15">
  <compounddef id="struct_x_m_c___p_o_s_i_f___h_s_c___c_o_n_f_i_g__t" kind="struct" language="C++" prot="public">
    <compoundname>XMC_POSIF_HSC_CONFIG_t</compoundname>
    <includes local="no">xmc_posif.h</includes>
      <sectiondef kind="public-attrib">
      <memberdef kind="variable" id="struct_x_m_c___p_o_s_i_f___h_s_c___c_o_n_f_i_g__t_1a3e9649795503c319c70a9123c1f775a7" prot="public" static="no" mutable="no">
        <type>union XMC_POSIF_HSC_CONFIG_t::@121</type>
        <definition>union XMC_POSIF_HSC_CONFIG_t::@121 @122</definition>
        <argsstring></argsstring>
        <name>@122</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_posif.h" line="333" column="1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___p_o_s_i_f___h_s_c___c_o_n_f_i_g__t_1a3e57c2ef1c3ffb36722f000cc1156824" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t __pad0__</definition>
        <argsstring></argsstring>
        <name>__pad0__</name>
        <bitfield> 4</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_posif.h" line="321" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_posif.h" bodystart="321" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___p_o_s_i_f___h_s_c___c_o_n_f_i_g__t_1a6712ba6dd1d5b43d2d56ff8ac4e275a7" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t __pad1__</definition>
        <argsstring></argsstring>
        <name>__pad1__</name>
        <bitfield> 11</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_posif.h" line="323" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_posif.h" bodystart="323" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___p_o_s_i_f___h_s_c___c_o_n_f_i_g__t_1a9ce12a63de64ef64ae2d59d128251cae" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t __pad2__</definition>
        <argsstring></argsstring>
        <name>__pad2__</name>
        <bitfield> 6</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_posif.h" line="326" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_posif.h" bodystart="326" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___p_o_s_i_f___h_s_c___c_o_n_f_i_g__t_1a4854608c0e776f0704a4d9a4b98ea57d" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t __pad3__</definition>
        <argsstring></argsstring>
        <name>__pad3__</name>
        <bitfield> 4</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_posif.h" line="330" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_posif.h" bodystart="330" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___p_o_s_i_f___h_s_c___c_o_n_f_i_g__t_1a804d9b698bff925068c656f775991439" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t disable_idle_signal</definition>
        <argsstring></argsstring>
        <name>disable_idle_signal</name>
        <bitfield> 1</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>Should idle signal be disabled upon wrong hall event? </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_posif.h" line="322" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_posif.h" bodystart="322" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___p_o_s_i_f___h_s_c___c_o_n_f_i_g__t_1a08605278e2711066a995f57b1da47d1d" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t external_error_enable</definition>
        <argsstring></argsstring>
        <name>external_error_enable</name>
        <bitfield> 1</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>Should external errors lead to Wrong Hall event? </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_posif.h" line="328" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_posif.h" bodystart="328" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___p_o_s_i_f___h_s_c___c_o_n_f_i_g__t_1a6d93920f43ca1c47f774ba8f3cfcdd07" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t external_error_level</definition>
        <argsstring></argsstring>
        <name>external_error_level</name>
        <bitfield> 1</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>What should be the active level of external error signal? </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_posif.h" line="329" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_posif.h" bodystart="329" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___p_o_s_i_f___h_s_c___c_o_n_f_i_g__t_1af5d0739130bd3f50b15b26b0ec8c4445" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t external_error_port</definition>
        <argsstring></argsstring>
        <name>external_error_port</name>
        <bitfield> 2</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>Of the 4 external error ports, which one is to be considered? </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_posif.h" line="327" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_posif.h" bodystart="327" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___p_o_s_i_f___h_s_c___c_o_n_f_i_g__t_1a63014e751d0342e02c71b88e29797d67" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t hall_config</definition>
        <argsstring></argsstring>
        <name>hall_config</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_posif.h" line="332" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_posif.h" bodystart="332" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___p_o_s_i_f___h_s_c___c_o_n_f_i_g__t_1a15213bb95c7cdce3cc920e64e2a19b71" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t sampling_trigger</definition>
        <argsstring></argsstring>
        <name>sampling_trigger</name>
        <bitfield> 1</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>Of HSDA and HSDB, which one is to be used to trigger POSIF to sample hall pattern? </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_posif.h" line="324" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_posif.h" bodystart="324" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___p_o_s_i_f___h_s_c___c_o_n_f_i_g__t_1af939bd2dc11d0c38e7db4a087407b7ea" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t sampling_trigger_edge</definition>
        <argsstring></argsstring>
        <name>sampling_trigger_edge</name>
        <bitfield> 1</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>Which edge of the sampling trigger signal is to be considered? </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_posif.h" line="325" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_posif.h" bodystart="325" bodyend="-1"/>
      </memberdef>
      </sectiondef>
    <briefdescription>
    </briefdescription>
    <detaileddescription>
<para>Defines POSIF hall sensor control initialization data structure. Use type <ref refid="struct_x_m_c___p_o_s_i_f___h_s_c___c_o_n_f_i_g__t" kindref="compound">XMC_POSIF_HSC_CONFIG_t</ref> for this data structure. It used to initialize hall sensor mode configuration using <emphasis>PCONF</emphasis> register. </para>
    </detaileddescription>
    <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_posif.h" line="316" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_posif.h" bodystart="315" bodyend="334"/>
    <listofallmembers>
      <member refid="struct_x_m_c___p_o_s_i_f___h_s_c___c_o_n_f_i_g__t_1a3e57c2ef1c3ffb36722f000cc1156824" prot="public" virt="non-virtual"><scope>XMC_POSIF_HSC_CONFIG_t</scope><name>__pad0__</name></member>
      <member refid="struct_x_m_c___p_o_s_i_f___h_s_c___c_o_n_f_i_g__t_1a6712ba6dd1d5b43d2d56ff8ac4e275a7" prot="public" virt="non-virtual"><scope>XMC_POSIF_HSC_CONFIG_t</scope><name>__pad1__</name></member>
      <member refid="struct_x_m_c___p_o_s_i_f___h_s_c___c_o_n_f_i_g__t_1a9ce12a63de64ef64ae2d59d128251cae" prot="public" virt="non-virtual"><scope>XMC_POSIF_HSC_CONFIG_t</scope><name>__pad2__</name></member>
      <member refid="struct_x_m_c___p_o_s_i_f___h_s_c___c_o_n_f_i_g__t_1a4854608c0e776f0704a4d9a4b98ea57d" prot="public" virt="non-virtual"><scope>XMC_POSIF_HSC_CONFIG_t</scope><name>__pad3__</name></member>
      <member refid="struct_x_m_c___p_o_s_i_f___h_s_c___c_o_n_f_i_g__t_1a804d9b698bff925068c656f775991439" prot="public" virt="non-virtual"><scope>XMC_POSIF_HSC_CONFIG_t</scope><name>disable_idle_signal</name></member>
      <member refid="struct_x_m_c___p_o_s_i_f___h_s_c___c_o_n_f_i_g__t_1a08605278e2711066a995f57b1da47d1d" prot="public" virt="non-virtual"><scope>XMC_POSIF_HSC_CONFIG_t</scope><name>external_error_enable</name></member>
      <member refid="struct_x_m_c___p_o_s_i_f___h_s_c___c_o_n_f_i_g__t_1a6d93920f43ca1c47f774ba8f3cfcdd07" prot="public" virt="non-virtual"><scope>XMC_POSIF_HSC_CONFIG_t</scope><name>external_error_level</name></member>
      <member refid="struct_x_m_c___p_o_s_i_f___h_s_c___c_o_n_f_i_g__t_1af5d0739130bd3f50b15b26b0ec8c4445" prot="public" virt="non-virtual"><scope>XMC_POSIF_HSC_CONFIG_t</scope><name>external_error_port</name></member>
      <member refid="struct_x_m_c___p_o_s_i_f___h_s_c___c_o_n_f_i_g__t_1a63014e751d0342e02c71b88e29797d67" prot="public" virt="non-virtual"><scope>XMC_POSIF_HSC_CONFIG_t</scope><name>hall_config</name></member>
      <member refid="struct_x_m_c___p_o_s_i_f___h_s_c___c_o_n_f_i_g__t_1a15213bb95c7cdce3cc920e64e2a19b71" prot="public" virt="non-virtual"><scope>XMC_POSIF_HSC_CONFIG_t</scope><name>sampling_trigger</name></member>
      <member refid="struct_x_m_c___p_o_s_i_f___h_s_c___c_o_n_f_i_g__t_1af939bd2dc11d0c38e7db4a087407b7ea" prot="public" virt="non-virtual"><scope>XMC_POSIF_HSC_CONFIG_t</scope><name>sampling_trigger_edge</name></member>
    </listofallmembers>
  </compounddef>
</doxygen>

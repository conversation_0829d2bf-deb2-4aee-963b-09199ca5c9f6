************
Contributors
************

Special thanks to all the people who spent their precious time and effort to help this project so far.
list contributors and their awesome work for the stack:

Notable contributors
====================

(sorted alphabetically)

`Adafruit Team <https://github.com/adafruit>`__
-----------------------------------------------

-  Main supporter and sponsor for hardware boards and kits
-  Discussion and suggestion for feature and improvement
-  Design the project logo


`Ha Thach <https://github.com/hathach>`__
-----------------------------------------

-  *Author and maintainer*
-  Most features development


`<PERSON><PERSON> <https://github.com/me-no-dev>`__
-------------------------------------------------

-  Improve ESP32s2 DCD


`<PERSON> <https://github.com/j4cbo>`__
------------------------------------------------

-  Add new class driver for network CDC-NCM


`Jan Dümpelmann <https://github.com/duempel>`__
-----------------------------------------------

-  Improve transfer performance for Synopsys DCD for STM32 MCUs


`Jeff Epler <https://github.com/jepler>`__
------------------------------------------

-  Improve MIDI class driver


`Jerzy Kasenberg <https://github.com/kasjer>`__
-----------------------------------------------

-  Add new DCD port for Dialog DA1469x
-  Add new class driver for Bluetooth HCI
-  Add ISO transfer for STM32 Synopsys, Nordic nRF, Dialog DA1469x
-  Improve Audio driver and add uac2\_headset example
-  Improve STM32 Synopsys DCD with various PRs


`J McCarthy <https://github.com/xmos-jmccarthy>`__
--------------------------------------------------

-  Add new DFU 1.1 class driver
-  Add new example for dfu


`Kamil Tomaszewski <https://github.com/kamtom480>`__
----------------------------------------------------

-  Add new DCD port for Sony CXD56 (spresnese board)


`Kay Sievers <https://github.com/kaysievers>`__
-----------------------------------------------

-  Improve MIDI driver with packet API


`Koji KITAYAMA <https://github.com/kkitayam>`__
-----------------------------------------------

-  Add new DCD port for NXP Kinetis KL25
-  Add new DCD port for Renesas RX family (RX600, RX700 ..) with GR-CITRUS, RX65n target board
-  Add new class driver for USB Video Class (UVC 1.5)


`Nathan Conrad <https://github.com/pigrew>`__
---------------------------------------------

-  Add new DCD port for STM32 fsdev Fullspeed device for STM32 L0,
   F0, F1, F3 etc ...
-  Add new class driver for USB Test and Measurement Class (USBTMC)
-  Various improvement e.g Zero-length packet, Lint setup
-  Board support for STM32F070RB Nucleo, STM32F303 Discovery


`Peter Lawrence <https://github.com/majbthrd>`__
------------------------------------------------

-  Add new DCD port for Nuvoton NUC 120, 121, 125, 126, 505
-  Add new class driver for network RNDIS, CDC-ECM
-  Enhance CDC-NCM network driver to compatible with RNDIS/ECM
-  Add *net\_lwip\_webserver* example for demonstration of usbnet with lwip
-  Board support for NuTiny NUC120, NUC121s, NUC125s, NUC126V, NUC505
-  Improve multiple cdc interfaces API & add cdc\_dual\_ports example


`Rafael Silva <https://github.com/perigoso>`__
----------------------------------------------

-  Port DCD Synopsys to support Silabs EFM32GG12 with SLTB009A board
-  Rewrite documentation in rst and setup for readthedocs


`Raspberry Pi Team <https://github.com/raspberrypi>`__
------------------------------------------------------

-  Add new DCD port for Raspberry Pi RP2040
-  Add new HCD port for Raspberry Pi RP2040


`Reinhard Panhuber <https://github.com/PanRe>`__
------------------------------------------------

-  Add new class driver for USB Audio Class 2.0 (UAC2)
-  Rework tu\_fifo with unmasked pointer, add DMA support, and constant address support
-  Add new DCD/USBD edpt\_xfer\_fifo() API for optimizing endpoint transfer
-  Add and greatly improve Isochronous transfer
-  Add new audio examples: audio\_test and audio\_4\_channel\_mic


`Scott Shawcroft <https://github.com/tannewt>`__
------------------------------------------------

-  Add new DCD port for SAMD21 and SAMD51
-  Add new class driver for Musical Instrument Digital Interface (MIDI)
-  Improve USBD control transfer, MSC, CDC class driver
-  Board support for Metro M0 & M4 express
-  Write the excellent porting.md documentation
-  Add initial Makefile

`Sean Cross <https://github.com/xobs>`__
----------------------------------------

-  Add new DCD port for ValentyUSB eptri (fomu board)


`Sylvain "tnt" Munaut <https://github.com/smunaut>`__
-----------------------------------------------------

-  Add new class driver for DFU Runtime


`Timon Skerutsch <https://github.com/PTS93>`__
----------------------------------------------

-  Add hid\_test.js script and extensive test for bi-directional raw HID


`Tod E. Kurt <https://github.com/todbot>`__
-------------------------------------------

-  Add hid\_test.js script and extensive test for bi-directional raw HID


`Uwe Bonnes <https://github.com/UweBonnes>`__
---------------------------------------------

-  Improve STM32 Synopsys highspeed DCD


`William D. Jones <https://github.com/cr1901>`__
------------------------------------------------

-  Add new DCD port for Synopsys DesignWare for STM32 L4, F2, F4,
   F7, H7 etc ...
-  Add new DCD port for TI MSP430
-  Board support for STM32F407 Discovery, STM32H743 Nucleo, pyboard v1.1, msp\_exp430f5529lp etc ...


`Zixun Li <https://github.com/HiFiPhile>`__
-------------------------------------------

-  Add new DCD port for Microchip SAMx7x
-  Add IAR compiler support
-  Improve UAC2, CDC, DFU class driver


`Full contributors list <https://github.com/hathach/tinyusb/contributors>`__
============================================================================

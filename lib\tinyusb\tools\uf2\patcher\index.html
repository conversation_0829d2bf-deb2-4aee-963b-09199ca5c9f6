<!DOCTYPE html>
<html lang="en-US">

<head>
  <meta charset="UTF-8">

  <title>CF2 binary patcher</title>

  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="theme-color" content="#157878">
  <link rel="stylesheet" href="https://microsoft.github.io/uf2/assets/css/style.css">
  <script src="patcher.js"></script>
  <script src="web.js"></script>
  <style>
    #patch {
      font-family: Consolas, "Liberation Mono", Menlo, Courier, monospace;
      font-size: 0.9rem;
      width: 100%;
      height: 12em;
    }

    .page-header {
      padding: 2rem 2rem !important;
    }

    .btn-dl,
    .btn-dl:hover {
      background-color: green;
      float: right;
      color: white;
    }

    footer {
      text-align: center;
      margin-top: 5em;
    }
    footer a {
      margin-right: 2em;
    }
  </style>
</head>

<body ondrop="dropHandler(event);" ondragover="dragOverHandler(event);">
  <section class="page-header">
    <h1 class="project-name">CF2 patcher</h1>
  </section>

  <section class="main-content">
    <h2>Your patch</h2>
    <textarea id="patch" spellcheck=false onchange="savePatch(event);"></textarea>
    <a href="#" id="apply" class="btn btn-dl">Apply my patch</a>

    <h2>Information</h2>
    <pre><code id="currconfig">Drop UF2 or BIN file above to see its config.</code></pre>

    <p>
      <a href="https://github.com/Microsoft/uf2/blob/master/cf2.md">Learn more</a>
      or
      <a href="#" onclick="defines()">download configkeys.h</a>.
    </p>


  <footer>
    <a href="https://makecode.com/privacy" target="_blank" rel="noopener">Privacy &amp; Cookies</a>
    <a href="https://makecode.com/termsofuse" target="_blank" rel="noopener"> Terms Of Use</a>
    <a href="https://makecode.com/trademarks" target="_blank" rel="noopener">Trademarks</a>
    <span>© 2019 Microsoft</span>
  </footer>

  </section>

  <script>
    restorePatch()
  </script>

</body>

</html>
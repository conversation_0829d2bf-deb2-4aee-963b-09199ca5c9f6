@echo off
REM 测试RTT printf修正 - 验证SEGGER_RTT_printf链接问题是否已解决

echo ========================================
echo 测试RTT printf链接修正
echo ========================================

REM 检查是否提供了板子名称参数
if "%1"=="" (
    echo 使用默认板子: feather_nrf52840_express
    set BOARD_NAME=feather_nrf52840_express
) else (
    set BOARD_NAME=%1
)

echo 目标板子: %BOARD_NAME%
echo.

echo [1/3] 清理之前的编译...
make BOARD=%BOARD_NAME% clean > nul 2>&1

echo [2/3] 测试编译单个文件...
echo 正在编译main.c...

REM 尝试编译main.c
make BOARD=%BOARD_NAME% DEBUG=1 _build/build-%BOARD_NAME%/src/main.o
if errorlevel 1 (
    echo ❌ main.c 编译失败!
    goto :end
) else (
    echo ✅ main.c 编译成功!
)

echo.
echo [3/3] 测试完整链接...
echo 正在进行完整编译和链接...

REM 尝试完整编译来测试链接
make BOARD=%BOARD_NAME% DEBUG=1 all
if errorlevel 1 (
    echo.
    echo ❌ 链接失败! 可能的问题:
    echo 1. SEGGER_RTT_printf 函数未定义
    echo 2. 缺少 SEGGER_RTT_printf.c 源文件
    echo 3. 其他链接错误
    echo.
    echo 已添加的修正:
    echo   C_SRC += $(RTT_SRC)/RTT/SEGGER_RTT_printf.c
    echo.
    echo 请检查具体的链接错误信息
    goto :end
) else (
    echo ✅ 完整编译和链接成功!
)

echo.
echo ========================================
echo ✅ RTT printf问题已解决!
echo ========================================
echo.
echo 修正内容:
echo   - 添加了 SEGGER_RTT_printf.c 到编译源文件
echo   - 现在 SEGGER_RTT_printf 函数可以正常链接
echo   - BLE设备名称: SEC_DFU
echo.
echo 生成的文件:
dir /b _bin\%BOARD_NAME%\*.hex 2>nul
echo.
echo 下一步可以:
echo 1. 烧录bootloader: make BOARD=%BOARD_NAME% flash
echo 2. 启动RTT Viewer查看调试输出
echo 3. 使用BLE扫描工具验证设备名称为 "SEC_DFU"

:end
pause

var searchData=
[
  ['ebu_5fale_5fmode',['ebu_ale_mode',['../struct_x_m_c___e_b_u___m_o_d_e___c_o_n_f_i_g__t.html#abccc76a0d96b0327d5b2f27a3f40cd63',1,'XMC_EBU_MODE_CONFIG_t']]],
  ['ebu_5farbitration_5fmode',['ebu_arbitration_mode',['../struct_x_m_c___e_b_u___m_o_d_e___c_o_n_f_i_g__t.html#ae3445813fc00a59b18c410f438a638c0',1,'XMC_EBU_MODE_CONFIG_t']]],
  ['ebu_5farbsync',['ebu_arbsync',['../struct_x_m_c___e_b_u___m_o_d_e___c_o_n_f_i_g__t.html#ac973f71e7c621d6b9d016d8f9586034a',1,'XMC_EBU_MODE_CONFIG_t']]],
  ['ebu_5fasynchronous_5faddress_5fphase',['ebu_asynchronous_address_phase',['../struct_x_m_c___e_b_u___b_u_s___r_e_a_d___c_o_n_f_i_g__t.html#aba6d3d9b4087e0fbd30916297a7cde6c',1,'XMC_EBU_BUS_READ_CONFIG_t::ebu_asynchronous_address_phase()'],['../struct_x_m_c___e_b_u___b_u_s___w_r_i_t_e___c_o_n_f_i_g__t.html#aba6d3d9b4087e0fbd30916297a7cde6c',1,'XMC_EBU_BUS_WRITE_CONFIG_t::ebu_asynchronous_address_phase()']]],
  ['ebu_5fburst_5faddress_5fwrapping',['ebu_burst_address_wrapping',['../struct_x_m_c___e_b_u___b_u_s___r_e_a_d___c_o_n_f_i_g__t.html#a8e144ecd1dbd215b536c9eb1caf5b668',1,'XMC_EBU_BUS_READ_CONFIG_t']]],
  ['ebu_5fburst_5fbuffer_5fsync_5fmode',['ebu_burst_buffer_sync_mode',['../struct_x_m_c___e_b_u___b_u_s___r_e_a_d___c_o_n_f_i_g__t.html#a01b291a106e937697eae13f2879f101c',1,'XMC_EBU_BUS_READ_CONFIG_t::ebu_burst_buffer_sync_mode()'],['../struct_x_m_c___e_b_u___b_u_s___w_r_i_t_e___c_o_n_f_i_g__t.html#a01b291a106e937697eae13f2879f101c',1,'XMC_EBU_BUS_WRITE_CONFIG_t::ebu_burst_buffer_sync_mode()']]],
  ['ebu_5fburst_5fflash_5fclock_5ffeedback',['ebu_burst_flash_clock_feedback',['../struct_x_m_c___e_b_u___b_u_s___r_e_a_d___c_o_n_f_i_g__t.html#a81e34dcfbf3e2d1b570ae3bd4c8c7343',1,'XMC_EBU_BUS_READ_CONFIG_t']]],
  ['ebu_5fburst_5fflash_5fclock_5fmode',['ebu_burst_flash_clock_mode',['../struct_x_m_c___e_b_u___b_u_s___r_e_a_d___c_o_n_f_i_g__t.html#a29365216885c14a9a8ad6d39c13f9c17',1,'XMC_EBU_BUS_READ_CONFIG_t']]],
  ['ebu_5fburst_5flength_5fsync',['ebu_burst_length_sync',['../struct_x_m_c___e_b_u___b_u_s___r_e_a_d___c_o_n_f_i_g__t.html#ac634adfa1698ddc79b4e820a053a117e',1,'XMC_EBU_BUS_READ_CONFIG_t::ebu_burst_length_sync()'],['../struct_x_m_c___e_b_u___b_u_s___w_r_i_t_e___c_o_n_f_i_g__t.html#ac634adfa1698ddc79b4e820a053a117e',1,'XMC_EBU_BUS_WRITE_CONFIG_t::ebu_burst_length_sync()']]],
  ['ebu_5fburst_5fsignal_5fsync',['ebu_burst_signal_sync',['../struct_x_m_c___e_b_u___b_u_s___r_e_a_d___c_o_n_f_i_g__t.html#a524e764e220487c3d8fe82e2eb257425',1,'XMC_EBU_BUS_READ_CONFIG_t::ebu_burst_signal_sync()'],['../struct_x_m_c___e_b_u___b_u_s___w_r_i_t_e___c_o_n_f_i_g__t.html#a524e764e220487c3d8fe82e2eb257425',1,'XMC_EBU_BUS_WRITE_CONFIG_t::ebu_burst_signal_sync()']]],
  ['ebu_5fbus_5fread_5fconfig',['ebu_bus_read_config',['../struct_x_m_c___e_b_u___r_e_g_i_o_n___r_e_a_d___c_o_n_f_i_g__t.html#aeb2207b4d6bf4d75e750e3bf0a87bcce',1,'XMC_EBU_REGION_READ_CONFIG_t']]],
  ['ebu_5fbus_5fwrite_5fconfig',['ebu_bus_write_config',['../struct_x_m_c___e_b_u___r_e_g_i_o_n___w_r_i_t_e___c_o_n_f_i_g__t.html#aadefa338202ff4a7d5ab00e598e6a7ae',1,'XMC_EBU_REGION_WRITE_CONFIG_t']]],
  ['ebu_5fbyte_5fcontrol',['ebu_byte_control',['../struct_x_m_c___e_b_u___b_u_s___r_e_a_d___c_o_n_f_i_g__t.html#a60cc2a4cd404631146e67bd5f7d68718',1,'XMC_EBU_BUS_READ_CONFIG_t::ebu_byte_control()'],['../struct_x_m_c___e_b_u___b_u_s___w_r_i_t_e___c_o_n_f_i_g__t.html#a60cc2a4cd404631146e67bd5f7d68718',1,'XMC_EBU_BUS_WRITE_CONFIG_t::ebu_byte_control()']]],
  ['ebu_5fclk_5fconfig',['ebu_clk_config',['../struct_x_m_c___e_b_u___c_o_n_f_i_g__t.html#ab02c6c260915332b1cc1ea0245652204',1,'XMC_EBU_CONFIG_t']]],
  ['ebu_5fclk_5fmode',['ebu_clk_mode',['../struct_x_m_c___e_b_u___c_l_k___c_o_n_f_i_g__t.html#ad753d31b9d0766627fd62ec39e4074b2',1,'XMC_EBU_CLK_CONFIG_t']]],
  ['ebu_5fclock_5fdivide_5fratio',['ebu_clock_divide_ratio',['../struct_x_m_c___e_b_u___c_l_k___c_o_n_f_i_g__t.html#a531271845fed54efdcc4ab40f2ad67bc',1,'XMC_EBU_CLK_CONFIG_t']]],
  ['ebu_5fdata_5fhold_5fcycles_5ffor_5fread_5faccesses',['ebu_data_hold_cycles_for_read_accesses',['../struct_x_m_c___e_b_u___b_u_s___r_e_a_d___c_o_n_f_i_g__t.html#a96bd64bc1f9d12ebea67327db71b86cf',1,'XMC_EBU_BUS_READ_CONFIG_t']]],
  ['ebu_5fdata_5fhold_5fcycles_5ffor_5fwrite_5faccesses',['ebu_data_hold_cycles_for_write_accesses',['../struct_x_m_c___e_b_u___b_u_s___w_r_i_t_e___c_o_n_f_i_g__t.html#a975095f8bc736439d1c852a1cff49650',1,'XMC_EBU_BUS_WRITE_CONFIG_t']]],
  ['ebu_5fdevice_5faddressing_5fmode',['ebu_device_addressing_mode',['../struct_x_m_c___e_b_u___b_u_s___r_e_a_d___c_o_n_f_i_g__t.html#a29e9c7035b94a479786a22717a5cbc43',1,'XMC_EBU_BUS_READ_CONFIG_t::ebu_device_addressing_mode()'],['../struct_x_m_c___e_b_u___b_u_s___w_r_i_t_e___c_o_n_f_i_g__t.html#a29e9c7035b94a479786a22717a5cbc43',1,'XMC_EBU_BUS_WRITE_CONFIG_t::ebu_device_addressing_mode()']]],
  ['ebu_5fdevice_5ftype',['ebu_device_type',['../struct_x_m_c___e_b_u___b_u_s___r_e_a_d___c_o_n_f_i_g__t.html#abd206212398403e7dba2f01376d5c6ad',1,'XMC_EBU_BUS_READ_CONFIG_t::ebu_device_type()'],['../struct_x_m_c___e_b_u___b_u_s___w_r_i_t_e___c_o_n_f_i_g__t.html#abd206212398403e7dba2f01376d5c6ad',1,'XMC_EBU_BUS_WRITE_CONFIG_t::ebu_device_type()']]],
  ['ebu_5fdiv2_5fclk_5fmode',['ebu_div2_clk_mode',['../struct_x_m_c___e_b_u___c_l_k___c_o_n_f_i_g__t.html#a03a46293c6499313c577a0b3d867ebdd',1,'XMC_EBU_CLK_CONFIG_t']]],
  ['ebu_5fearly_5fchip_5fselect_5fsync_5fburst',['ebu_early_chip_select_sync_burst',['../struct_x_m_c___e_b_u___b_u_s___r_e_a_d___c_o_n_f_i_g__t.html#ad96e9c8692551b122a74eddfe62fdab2',1,'XMC_EBU_BUS_READ_CONFIG_t::ebu_early_chip_select_sync_burst()'],['../struct_x_m_c___e_b_u___b_u_s___w_r_i_t_e___c_o_n_f_i_g__t.html#ad96e9c8692551b122a74eddfe62fdab2',1,'XMC_EBU_BUS_WRITE_CONFIG_t::ebu_early_chip_select_sync_burst()']]],
  ['ebu_5fext_5fdata',['ebu_ext_data',['../struct_x_m_c___e_b_u___b_u_s___r_e_a_d___c_o_n_f_i_g__t.html#a486d75ee6ea52a670b6648243a74a2d5',1,'XMC_EBU_BUS_READ_CONFIG_t::ebu_ext_data()'],['../struct_x_m_c___e_b_u___b_u_s___w_r_i_t_e___c_o_n_f_i_g__t.html#a486d75ee6ea52a670b6648243a74a2d5',1,'XMC_EBU_BUS_WRITE_CONFIG_t::ebu_ext_data()']]],
  ['ebu_5fextlock',['ebu_extlock',['../struct_x_m_c___e_b_u___m_o_d_e___c_o_n_f_i_g__t.html#a6ad219a708060c7d9a080ceba1a47575',1,'XMC_EBU_MODE_CONFIG_t']]],
  ['ebu_5fflash_5fnon_5farray_5faccess',['ebu_flash_non_array_access',['../struct_x_m_c___e_b_u___b_u_s___r_e_a_d___c_o_n_f_i_g__t.html#a379a83c0e94efa6fb2f551760fb463e3',1,'XMC_EBU_BUS_READ_CONFIG_t']]],
  ['ebu_5ffree_5fpins_5fto_5fgpio',['ebu_free_pins_to_gpio',['../struct_x_m_c___e_b_u___c_o_n_f_i_g__t.html#ab0cce0ad6462964ac89f734734d091c1',1,'XMC_EBU_CONFIG_t']]],
  ['ebu_5ffreq_5fext_5fclk_5fpin',['ebu_freq_ext_clk_pin',['../struct_x_m_c___e_b_u___b_u_s___r_e_a_d___c_o_n_f_i_g__t.html#afca82a90601a18a29303264cc3ea1026',1,'XMC_EBU_BUS_READ_CONFIG_t']]],
  ['ebu_5finit_5frefresh_5fcommands_5fcounter',['ebu_init_refresh_commands_counter',['../struct_x_m_c___e_b_u___s_d_r_a_m___c_o_n_f_i_g__t.html#af4e565ca82d73897629a7dbf737c5462',1,'XMC_EBU_SDRAM_CONFIG_t']]],
  ['ebu_5flock_5fchip_5fselect',['ebu_lock_chip_select',['../struct_x_m_c___e_b_u___b_u_s___w_r_i_t_e___c_o_n_f_i_g__t.html#ac1af132da760c10c167c42c6a584e727',1,'XMC_EBU_BUS_WRITE_CONFIG_t']]],
  ['ebu_5fmode_5fconfig',['ebu_mode_config',['../struct_x_m_c___e_b_u___c_o_n_f_i_g__t.html#a6edd9d8eedb196cdfca5e07f5dc0f1a6',1,'XMC_EBU_CONFIG_t']]],
  ['ebu_5fmode_5fregister_5fset_5fup_5ftime',['ebu_mode_register_set_up_time',['../struct_x_m_c___e_b_u___s_d_r_a_m___c_o_n_f_i_g__t.html#a40fe29a2c5bccc5cfd0e392ac04bbcc8',1,'XMC_EBU_SDRAM_CONFIG_t']]],
  ['ebu_5fprogrammed_5fwait_5fstates_5ffor_5fread_5faccesses',['ebu_programmed_wait_states_for_read_accesses',['../struct_x_m_c___e_b_u___b_u_s___r_e_a_d___c_o_n_f_i_g__t.html#ae54fd1c77c48af9bfb8606d6b0104c82',1,'XMC_EBU_BUS_READ_CONFIG_t']]],
  ['ebu_5fprogrammed_5fwait_5fstates_5ffor_5fwrite_5faccesses',['ebu_programmed_wait_states_for_write_accesses',['../struct_x_m_c___e_b_u___b_u_s___w_r_i_t_e___c_o_n_f_i_g__t.html#a48d18bc892df1e312555870694ba9836',1,'XMC_EBU_BUS_WRITE_CONFIG_t']]],
  ['ebu_5fread_5fstages_5fsynch',['ebu_read_stages_synch',['../struct_x_m_c___e_b_u___b_u_s___r_e_a_d___c_o_n_f_i_g__t.html#a99a679932478fe1e98c63efdd8725b76',1,'XMC_EBU_BUS_READ_CONFIG_t']]],
  ['ebu_5frecovery_5fcycles_5fafter_5fread_5faccesses',['ebu_recovery_cycles_after_read_accesses',['../struct_x_m_c___e_b_u___b_u_s___r_e_a_d___c_o_n_f_i_g__t.html#ae468133f8f830ef27d7a18423c8516cc',1,'XMC_EBU_BUS_READ_CONFIG_t']]],
  ['ebu_5frecovery_5fcycles_5fafter_5fwrite_5faccesses',['ebu_recovery_cycles_after_write_accesses',['../struct_x_m_c___e_b_u___b_u_s___w_r_i_t_e___c_o_n_f_i_g__t.html#a67e71694f05158d408408291058026a2',1,'XMC_EBU_BUS_WRITE_CONFIG_t']]],
  ['ebu_5frecovery_5fcycles_5fbetween_5fdifferent_5fregions',['ebu_recovery_cycles_between_different_regions',['../struct_x_m_c___e_b_u___b_u_s___r_e_a_d___c_o_n_f_i_g__t.html#ae76b5e0c33547d41f4d2defef9ebe529',1,'XMC_EBU_BUS_READ_CONFIG_t::ebu_recovery_cycles_between_different_regions()'],['../struct_x_m_c___e_b_u___b_u_s___w_r_i_t_e___c_o_n_f_i_g__t.html#ae76b5e0c33547d41f4d2defef9ebe529',1,'XMC_EBU_BUS_WRITE_CONFIG_t::ebu_recovery_cycles_between_different_regions()']]],
  ['ebu_5fregion_5fno',['ebu_region_no',['../struct_x_m_c___e_b_u___r_e_g_i_o_n___r_e_a_d___c_o_n_f_i_g__t.html#a4da90c3eb821e8f678f4ad92e9af02cb',1,'XMC_EBU_REGION_READ_CONFIG_t::ebu_region_no()'],['../struct_x_m_c___e_b_u___r_e_g_i_o_n___w_r_i_t_e___c_o_n_f_i_g__t.html#a4da90c3eb821e8f678f4ad92e9af02cb',1,'XMC_EBU_REGION_WRITE_CONFIG_t::ebu_region_no()']]],
  ['ebu_5frow_5fprecharge_5fdelay_5fcounter',['ebu_row_precharge_delay_counter',['../struct_x_m_c___e_b_u___s_d_r_a_m___c_o_n_f_i_g__t.html#a4d056c9e4519ced599b69c3b1f0fcad2',1,'XMC_EBU_SDRAM_CONFIG_t']]],
  ['ebu_5frow_5fprecharge_5ftime_5fcounter',['ebu_row_precharge_time_counter',['../struct_x_m_c___e_b_u___s_d_r_a_m___c_o_n_f_i_g__t.html#a3409310c7a4d0a8091ad28d49e6a4852',1,'XMC_EBU_SDRAM_CONFIG_t']]],
  ['ebu_5fsdram_5fauto_5frefresh',['ebu_sdram_auto_refresh',['../struct_x_m_c___e_b_u___s_d_r_a_m___c_o_n_f_i_g__t.html#aae8c10215b40bfa05e253859f8182642',1,'XMC_EBU_SDRAM_CONFIG_t']]],
  ['ebu_5fsdram_5fauto_5fself_5frefresh',['ebu_sdram_auto_self_refresh',['../struct_x_m_c___e_b_u___s_d_r_a_m___c_o_n_f_i_g__t.html#a1efe25dc0b5e60a651e99cb7f352ac79',1,'XMC_EBU_SDRAM_CONFIG_t']]],
  ['ebu_5fsdram_5fburst_5flength',['ebu_sdram_burst_length',['../struct_x_m_c___e_b_u___s_d_r_a_m___c_o_n_f_i_g__t.html#a454cde2e634f6d533904fe35c9fea0b9',1,'XMC_EBU_SDRAM_CONFIG_t']]],
  ['ebu_5fsdram_5fcasclk_5fmode',['ebu_sdram_casclk_mode',['../struct_x_m_c___e_b_u___s_d_r_a_m___c_o_n_f_i_g__t.html#a78cd743c02cb5db5551dfa8d98fc24b9',1,'XMC_EBU_SDRAM_CONFIG_t']]],
  ['ebu_5fsdram_5fclk_5fmode',['ebu_sdram_clk_mode',['../struct_x_m_c___e_b_u___s_d_r_a_m___c_o_n_f_i_g__t.html#a6ab4b3b207606794c3409ced7ca4567f',1,'XMC_EBU_SDRAM_CONFIG_t']]],
  ['ebu_5fsdram_5fclk_5foutput',['ebu_sdram_clk_output',['../struct_x_m_c___e_b_u___s_d_r_a_m___c_o_n_f_i_g__t.html#a5f17ed98d3a8d59ec6aa180224110c08',1,'XMC_EBU_SDRAM_CONFIG_t']]],
  ['ebu_5fsdram_5fcold_5fstart',['ebu_sdram_cold_start',['../struct_x_m_c___e_b_u___s_d_r_a_m___c_o_n_f_i_g__t.html#ad8499e170147036dfb7298ba352c4899',1,'XMC_EBU_SDRAM_CONFIG_t']]],
  ['ebu_5fsdram_5fdelay_5fon_5fpower_5fdown_5fexit',['ebu_sdram_delay_on_power_down_exit',['../struct_x_m_c___e_b_u___s_d_r_a_m___c_o_n_f_i_g__t.html#aa11c9f4cc67b1f89d257ab28cd9a22f0',1,'XMC_EBU_SDRAM_CONFIG_t']]],
  ['ebu_5fsdram_5fextended_5foperation_5fbank_5fselect',['ebu_sdram_extended_operation_bank_select',['../struct_x_m_c___e_b_u___s_d_r_a_m___c_o_n_f_i_g__t.html#abedf8afc03ffa1d42fe82019641d0dc4',1,'XMC_EBU_SDRAM_CONFIG_t']]],
  ['ebu_5fsdram_5fextended_5foperation_5fmode',['ebu_sdram_extended_operation_mode',['../struct_x_m_c___e_b_u___s_d_r_a_m___c_o_n_f_i_g__t.html#accfdeb761752180bb9e473755827cb62',1,'XMC_EBU_SDRAM_CONFIG_t']]],
  ['ebu_5fsdram_5fextended_5frefresh_5fcounter_5fperiod',['ebu_sdram_extended_refresh_counter_period',['../struct_x_m_c___e_b_u___s_d_r_a_m___c_o_n_f_i_g__t.html#ae4ad4fdb0be155ef9783237535ab36e9',1,'XMC_EBU_SDRAM_CONFIG_t']]],
  ['ebu_5fsdram_5fmask_5ffor_5fbank_5ftag',['ebu_sdram_mask_for_bank_tag',['../struct_x_m_c___e_b_u___s_d_r_a_m___c_o_n_f_i_g__t.html#a92715b692e7835e6cd5296215adc8f4a',1,'XMC_EBU_SDRAM_CONFIG_t']]],
  ['ebu_5fsdram_5fmask_5ffor_5frow_5ftag',['ebu_sdram_mask_for_row_tag',['../struct_x_m_c___e_b_u___s_d_r_a_m___c_o_n_f_i_g__t.html#a21f6ab94fb555eba343580f21ed5c900',1,'XMC_EBU_SDRAM_CONFIG_t']]],
  ['ebu_5fsdram_5fnum_5frefresh_5fcmnds',['ebu_sdram_num_refresh_cmnds',['../struct_x_m_c___e_b_u___s_d_r_a_m___c_o_n_f_i_g__t.html#a98d36da29f058438e3b0c1f6275ae52e',1,'XMC_EBU_SDRAM_CONFIG_t']]],
  ['ebu_5fsdram_5fnum_5frefresh_5fcounter_5fperiod',['ebu_sdram_num_refresh_counter_period',['../struct_x_m_c___e_b_u___s_d_r_a_m___c_o_n_f_i_g__t.html#a2e58fcd8cb208b2747e2860437da3a01',1,'XMC_EBU_SDRAM_CONFIG_t']]],
  ['ebu_5fsdram_5fpwr_5fmode',['ebu_sdram_pwr_mode',['../struct_x_m_c___e_b_u___s_d_r_a_m___c_o_n_f_i_g__t.html#a2e2bc42e7ea4972f0a8e432652f50beb',1,'XMC_EBU_SDRAM_CONFIG_t']]],
  ['ebu_5fsdram_5frow_5fcycle_5ftime_5fcounter',['ebu_sdram_row_cycle_time_counter',['../struct_x_m_c___e_b_u___s_d_r_a_m___c_o_n_f_i_g__t.html#ae324a28fac6e271db6bd1c558b50f3cb',1,'XMC_EBU_SDRAM_CONFIG_t']]],
  ['ebu_5fsdram_5frow_5fcycle_5ftime_5fcounter_5fextension',['ebu_sdram_row_cycle_time_counter_extension',['../struct_x_m_c___e_b_u___s_d_r_a_m___c_o_n_f_i_g__t.html#a2ac2607b97cd2ae9e2ee54ca37813adf',1,'XMC_EBU_SDRAM_CONFIG_t']]],
  ['ebu_5fsdram_5frow_5fto_5fcolumn_5fdelay_5fcounter',['ebu_sdram_row_to_column_delay_counter',['../struct_x_m_c___e_b_u___s_d_r_a_m___c_o_n_f_i_g__t.html#a7363a4776d2a2b58c0bbd07c96704154',1,'XMC_EBU_SDRAM_CONFIG_t']]],
  ['ebu_5fsdram_5fself_5frefresh_5fentry',['ebu_sdram_self_refresh_entry',['../struct_x_m_c___e_b_u___s_d_r_a_m___c_o_n_f_i_g__t.html#a333aadd878f5dfa3080070b1830ffbf7',1,'XMC_EBU_SDRAM_CONFIG_t']]],
  ['ebu_5fsdram_5fself_5frefresh_5fexit',['ebu_sdram_self_refresh_exit',['../struct_x_m_c___e_b_u___s_d_r_a_m___c_o_n_f_i_g__t.html#aa8bb194d7daa87ead2c4f7b165ace59b',1,'XMC_EBU_SDRAM_CONFIG_t']]],
  ['ebu_5fsdram_5fself_5frefresh_5fexit_5fdelay',['ebu_sdram_self_refresh_exit_delay',['../struct_x_m_c___e_b_u___s_d_r_a_m___c_o_n_f_i_g__t.html#ab84982ac7e0b1f07961445d8efaafad2',1,'XMC_EBU_SDRAM_CONFIG_t']]],
  ['ebu_5fsdram_5ftristate',['ebu_sdram_tristate',['../struct_x_m_c___e_b_u___m_o_d_e___c_o_n_f_i_g__t.html#a1954f429c26701371861793627a7116e',1,'XMC_EBU_MODE_CONFIG_t']]],
  ['ebu_5fsdram_5fwidth_5fof_5fcolumn_5faddress',['ebu_sdram_width_of_column_address',['../struct_x_m_c___e_b_u___s_d_r_a_m___c_o_n_f_i_g__t.html#a4ada8d7b61edbb0403745b63c6c57692',1,'XMC_EBU_SDRAM_CONFIG_t']]],
  ['ebu_5fwait_5fcontrol',['ebu_wait_control',['../struct_x_m_c___e_b_u___b_u_s___r_e_a_d___c_o_n_f_i_g__t.html#af85dd7c1ae0f2e95bec9e3184d8b9c95',1,'XMC_EBU_BUS_READ_CONFIG_t::ebu_wait_control()'],['../struct_x_m_c___e_b_u___b_u_s___w_r_i_t_e___c_o_n_f_i_g__t.html#af85dd7c1ae0f2e95bec9e3184d8b9c95',1,'XMC_EBU_BUS_WRITE_CONFIG_t::ebu_wait_control()']]],
  ['ebu_5fwait_5fsignal_5fpolarity',['ebu_wait_signal_polarity',['../struct_x_m_c___e_b_u___b_u_s___r_e_a_d___c_o_n_f_i_g__t.html#ada6921f9e15ca117281fda8fb9abfbae',1,'XMC_EBU_BUS_READ_CONFIG_t::ebu_wait_signal_polarity()'],['../struct_x_m_c___e_b_u___b_u_s___w_r_i_t_e___c_o_n_f_i_g__t.html#ada6921f9e15ca117281fda8fb9abfbae',1,'XMC_EBU_BUS_WRITE_CONFIG_t::ebu_wait_signal_polarity()']]],
  ['edge',['edge',['../struct_x_m_c___c_c_u4___s_l_i_c_e___e_v_e_n_t___c_o_n_f_i_g__t.html#a19fa53123a0775ac92dc99d741f1395a',1,'XMC_CCU4_SLICE_EVENT_CONFIG_t::edge()'],['../struct_x_m_c___c_c_u8___s_l_i_c_e___e_v_e_n_t___c_o_n_f_i_g__t.html#a4132bee9f09ed2c6dc928efcc84f709e',1,'XMC_CCU8_SLICE_EVENT_CONFIG_t::edge()'],['../struct_x_m_c___h_r_p_w_m___c_s_g___i_n_p_u_t___c_o_n_f_i_g__t.html#a06cd86f88f58fba9ea9462198b9ff4c4',1,'XMC_HRPWM_CSG_INPUT_CONFIG_t::edge()']]],
  ['edge_5fdetection',['edge_detection',['../struct_x_m_c___e_r_u___e_t_l___c_o_n_f_i_g__t.html#a23f005cc2d2c65241e0b6ce5f52b9b0c',1,'XMC_ERU_ETL_CONFIG_t']]],
  ['emux_5fchannel_5fnumber',['emux_channel_number',['../struct_x_m_c___v_a_d_c___g_l_o_b_a_l___d_e_t_a_i_l_e_d___r_e_s_u_l_t__t.html#af18431e0f7cefd721fd79ba1ddf9dab9',1,'XMC_VADC_GLOBAL_DETAILED_RESULT_t::emux_channel_number()'],['../struct_x_m_c___v_a_d_c___d_e_t_a_i_l_e_d___r_e_s_u_l_t__t.html#af18431e0f7cefd721fd79ba1ddf9dab9',1,'XMC_VADC_DETAILED_RESULT_t::emux_channel_number()']]],
  ['emux_5fcoding',['emux_coding',['../struct_x_m_c___v_a_d_c___g_r_o_u_p___e_m_u_x_c_f_g__t.html#a0c0bcdd01551639ead516db9d74298f3',1,'XMC_VADC_GROUP_EMUXCFG_t']]],
  ['emux_5fconfig',['emux_config',['../struct_x_m_c___v_a_d_c___g_r_o_u_p___c_o_n_f_i_g__t.html#ac8bb4a20f80378f827f717f85f44de7a',1,'XMC_VADC_GROUP_CONFIG_t']]],
  ['emux_5fmode',['emux_mode',['../struct_x_m_c___v_a_d_c___g_r_o_u_p___e_m_u_x_c_f_g__t.html#a67b079297a01fecd839fd79219a41185',1,'XMC_VADC_GROUP_EMUXCFG_t']]],
  ['enable_5fauto_5fnegotiate',['enable_auto_negotiate',['../struct_x_m_c___e_t_h___p_h_y___c_o_n_f_i_g__t.html#a593beca42f9f65af838bacbea7981cbe',1,'XMC_ETH_PHY_CONFIG_t']]],
  ['enable_5fauto_5fscan',['enable_auto_scan',['../struct_x_m_c___v_a_d_c___s_c_a_n___c_o_n_f_i_g__t.html#af49959bd20deb85fe54ffcb06caea8e2',1,'XMC_VADC_SCAN_CONFIG_t']]],
  ['enable_5fdead_5ftime_5fchannel1',['enable_dead_time_channel1',['../struct_x_m_c___c_c_u8___s_l_i_c_e___d_e_a_d___t_i_m_e___c_o_n_f_i_g__t.html#a2f26dcacfc3a9dd79c1212315ad1dd00',1,'XMC_CCU8_SLICE_DEAD_TIME_CONFIG_t']]],
  ['enable_5fdead_5ftime_5fchannel2',['enable_dead_time_channel2',['../struct_x_m_c___c_c_u8___s_l_i_c_e___d_e_a_d___t_i_m_e___c_o_n_f_i_g__t.html#ac3076291941326a03b8a0baf7ec0e14f',1,'XMC_CCU8_SLICE_DEAD_TIME_CONFIG_t']]],
  ['enable_5fdst_5flinked_5flist',['enable_dst_linked_list',['../struct_x_m_c___d_m_a___l_l_i__t.html#a3f7f46d04af68ef4debbcabe619980da',1,'XMC_DMA_LLI_t']]],
  ['enable_5fdst_5fscatter',['enable_dst_scatter',['../struct_x_m_c___d_m_a___l_l_i__t.html#a7ebf6417cf37ad64e574c6cc1b049f87',1,'XMC_DMA_LLI_t::enable_dst_scatter()'],['../struct_x_m_c___d_m_a___c_h___c_o_n_f_i_g__t.html#a7ebf6417cf37ad64e574c6cc1b049f87',1,'XMC_DMA_CH_CONFIG_t::enable_dst_scatter()']]],
  ['enable_5fintegrator_5fcoupling',['enable_integrator_coupling',['../struct_x_m_c___d_s_d___c_h___a_u_x___f_i_l_t_e_r___c_o_n_f_i_g__t.html#a900e1cc0c015dd8b3eee4f26bef07ef3',1,'XMC_DSD_CH_AUX_FILTER_CONFIG_t']]],
  ['enable_5finterrupt',['enable_interrupt',['../struct_x_m_c___d_m_a___l_l_i__t.html#ab4451ed74feb37e9f912ef7bb15ba250',1,'XMC_DMA_LLI_t::enable_interrupt()'],['../struct_x_m_c___d_m_a___c_h___c_o_n_f_i_g__t.html#ab4451ed74feb37e9f912ef7bb15ba250',1,'XMC_DMA_CH_CONFIG_t::enable_interrupt()']]],
  ['enable_5floop_5fback',['enable_loop_back',['../struct_x_m_c___e_t_h___p_h_y___c_o_n_f_i_g__t.html#a873483842749d5c85874b713f111d6cf',1,'XMC_ETH_PHY_CONFIG_t']]],
  ['enable_5foschp',['enable_oschp',['../struct_x_m_c___s_c_u___c_l_o_c_k___c_o_n_f_i_g__t.html#a11fa42f2fb14a29c2c52c6cf4d2686e3',1,'XMC_SCU_CLOCK_CONFIG_t']]],
  ['enable_5fosculp',['enable_osculp',['../struct_x_m_c___s_c_u___c_l_o_c_k___c_o_n_f_i_g__t.html#ab8f0425906a8dc65344a06085c4537f5',1,'XMC_SCU_CLOCK_CONFIG_t']]],
  ['enable_5foutput_5ftrigger',['enable_output_trigger',['../struct_x_m_c___e_r_u___e_t_l___c_o_n_f_i_g__t.html#a10dc323300efdb1e8b8ffc18ecfd3c65',1,'XMC_ERU_ETL_CONFIG_t']]],
  ['enable_5fpattern_5fdetection',['enable_pattern_detection',['../union_x_m_c___e_r_u___o_g_u___c_o_n_f_i_g__t.html#abcc483ab4303eadec1b836df7d0fbcb6',1,'XMC_ERU_OGU_CONFIG_t']]],
  ['enable_5frstreq',['enable_rstreq',['../struct_x_m_c___e_c_a_t___p_o_r_t___c_t_r_l__t.html#af5105c675c5eaa94251e21ad545bc202',1,'XMC_ECAT_PORT_CTRL_t']]],
  ['enable_5fsrc_5fgather',['enable_src_gather',['../struct_x_m_c___d_m_a___l_l_i__t.html#acc4bff36f5f630e74eab2eb14bfc3b4f',1,'XMC_DMA_LLI_t::enable_src_gather()'],['../struct_x_m_c___d_m_a___c_h___c_o_n_f_i_g__t.html#acc4bff36f5f630e74eab2eb14bfc3b4f',1,'XMC_DMA_CH_CONFIG_t::enable_src_gather()']]],
  ['enable_5fsrc_5flinked_5flist',['enable_src_linked_list',['../struct_x_m_c___d_m_a___l_l_i__t.html#a778cb0f865db4fef2d37e5cee8a6244e',1,'XMC_DMA_LLI_t']]],
  ['endpoint_5fin_5fregister',['endpoint_in_register',['../struct_x_m_c___u_s_b_d___d_e_v_i_c_e__t.html#adf77c1f00c3290b33c8a6b7a0c6b0fa0',1,'XMC_USBD_DEVICE_t']]],
  ['endpoint_5fout_5fregister',['endpoint_out_register',['../struct_x_m_c___u_s_b_d___d_e_v_i_c_e__t.html#a816cfe15b81f83b360c655b5019d03df',1,'XMC_USBD_DEVICE_t']]],
  ['endpointabort',['EndpointAbort',['../struct_x_m_c___u_s_b_d___d_r_i_v_e_r__t.html#ac7f80e706ed0ad53512ce2afc3ce5d03',1,'XMC_USBD_DRIVER_t']]],
  ['endpointconfigure',['EndpointConfigure',['../struct_x_m_c___u_s_b_d___d_r_i_v_e_r__t.html#afc51c412d86ace2d89c65a77739fb5eb',1,'XMC_USBD_DRIVER_t']]],
  ['endpointevent_5fcb',['EndpointEvent_cb',['../struct_x_m_c___u_s_b_d___d_e_v_i_c_e__t.html#afc697e1dc7a5c5d7155f71006c77ef03',1,'XMC_USBD_DEVICE_t']]],
  ['endpointread',['EndpointRead',['../struct_x_m_c___u_s_b_d___d_r_i_v_e_r__t.html#a1223f2b9639874018424bf09ff5f4338',1,'XMC_USBD_DRIVER_t']]],
  ['endpointreadstart',['EndpointReadStart',['../struct_x_m_c___u_s_b_d___d_r_i_v_e_r__t.html#a34382fae66e9a56d4f691d1f8d74d3a8',1,'XMC_USBD_DRIVER_t']]],
  ['endpointstall',['EndpointStall',['../struct_x_m_c___u_s_b_d___d_r_i_v_e_r__t.html#a1cfd853df025d4032129382aef85a802',1,'XMC_USBD_DRIVER_t']]],
  ['endpointunconfigure',['EndpointUnconfigure',['../struct_x_m_c___u_s_b_d___d_r_i_v_e_r__t.html#aa247d0ce1d2ae95e5eab02bb8b61ec0c',1,'XMC_USBD_DRIVER_t']]],
  ['endpointwrite',['EndpointWrite',['../struct_x_m_c___u_s_b_d___d_r_i_v_e_r__t.html#aff2634092074c1191f9ae8660ead9668',1,'XMC_USBD_DRIVER_t']]],
  ['ep',['ep',['../struct_x_m_c___u_s_b_d___d_e_v_i_c_e__t.html#ac1f09802a53a8988d55b2550fdb4be95',1,'XMC_USBD_DEVICE_t']]],
  ['ep_5fmax_5fpacket_5fsize',['ep_max_packet_size',['../struct_x_m_c___u_s_b_h0__pipe__t.html#a20d471c00d2e0c0b055cb2c8157ed530',1,'XMC_USBH0_pipe_t']]],
  ['ep_5ftype',['ep_type',['../struct_x_m_c___u_s_b_h0__pipe__t.html#a791714ade336270a9c8fafef311b28f0',1,'XMC_USBH0_pipe_t']]],
  ['event',['event',['../struct_x_m_c___u_s_b_h0__pipe__t.html#aeef6900f411bc223febdd92c9435693b',1,'XMC_USBH0_pipe_t']]],
  ['event_5fconnect',['event_connect',['../struct_x_m_c___u_s_b_d___c_a_p_a_b_i_l_i_t_i_e_s__t.html#ae76b779cb9fdf447b20c8b6beed2d534',1,'XMC_USBD_CAPABILITIES_t::event_connect()'],['../struct_x_m_c___u_s_b_h___c_a_p_a_b_i_l_i_t_i_e_s__t.html#ae76b779cb9fdf447b20c8b6beed2d534',1,'XMC_USBH_CAPABILITIES_t::event_connect()']]],
  ['event_5fdisconnect',['event_disconnect',['../struct_x_m_c___u_s_b_d___c_a_p_a_b_i_l_i_t_i_e_s__t.html#ab83941051cac8e19807b887354dc42fc',1,'XMC_USBD_CAPABILITIES_t::event_disconnect()'],['../struct_x_m_c___u_s_b_h___c_a_p_a_b_i_l_i_t_i_e_s__t.html#ab83941051cac8e19807b887354dc42fc',1,'XMC_USBH_CAPABILITIES_t::event_disconnect()']]],
  ['event_5fgen_5fcriteria',['event_gen_criteria',['../struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t.html#ad6d48cf05e253058ff97116a5328d605',1,'XMC_VADC_CHANNEL_CONFIG_t']]],
  ['event_5fgen_5fenable',['event_gen_enable',['../struct_x_m_c___v_a_d_c___g_l_o_b_a_l___c_o_n_f_i_g__t.html#a8b629d8f3ceb3335a384b5f5b443ad8b',1,'XMC_VADC_GLOBAL_CONFIG_t::event_gen_enable()'],['../struct_x_m_c___v_a_d_c___r_e_s_u_l_t___c_o_n_f_i_g__t.html#a8b629d8f3ceb3335a384b5f5b443ad8b',1,'XMC_VADC_RESULT_CONFIG_t::event_gen_enable()']]],
  ['event_5fhigh_5fspeed',['event_high_speed',['../struct_x_m_c___u_s_b_d___c_a_p_a_b_i_l_i_t_i_e_s__t.html#a2c9db17c7e3932ce32a0ac5939252978',1,'XMC_USBD_CAPABILITIES_t']]],
  ['event_5fovercurrent',['event_overcurrent',['../struct_x_m_c___u_s_b_h___c_a_p_a_b_i_l_i_t_i_e_s__t.html#acd3087b3a4a7691595dd75568c12d696',1,'XMC_USBH_CAPABILITIES_t']]],
  ['event_5fpower_5foff',['event_power_off',['../struct_x_m_c___u_s_b_d___c_a_p_a_b_i_l_i_t_i_e_s__t.html#a68ddc21060b235c6d58a021ee47ad090',1,'XMC_USBD_CAPABILITIES_t']]],
  ['event_5fpower_5fon',['event_power_on',['../struct_x_m_c___u_s_b_d___c_a_p_a_b_i_l_i_t_i_e_s__t.html#abef08721d9b61eec760e9047b974cb1b',1,'XMC_USBD_CAPABILITIES_t']]],
  ['event_5fremote_5fwakeup',['event_remote_wakeup',['../struct_x_m_c___u_s_b_d___c_a_p_a_b_i_l_i_t_i_e_s__t.html#a1a52772964e67d94a56242b279fae2cb',1,'XMC_USBD_CAPABILITIES_t']]],
  ['event_5freset',['event_reset',['../struct_x_m_c___u_s_b_d___c_a_p_a_b_i_l_i_t_i_e_s__t.html#aac1a263f09fc02988eeed8d12957f333',1,'XMC_USBD_CAPABILITIES_t']]],
  ['event_5fresume',['event_resume',['../struct_x_m_c___u_s_b_d___c_a_p_a_b_i_l_i_t_i_e_s__t.html#afd5808847c1e47e36408ae77c08363c9',1,'XMC_USBD_CAPABILITIES_t']]],
  ['event_5fsuspend',['event_suspend',['../struct_x_m_c___u_s_b_d___c_a_p_a_b_i_l_i_t_i_e_s__t.html#a461d25bd74e3c2ce2c89a338162983c8',1,'XMC_USBD_CAPABILITIES_t']]],
  ['evfr',['EVFR',['../struct_x_m_c___l_e_d_t_s___g_l_o_b_a_l__t.html#ad3606c907984ebf611645aa5bc498239',1,'XMC_LEDTS_GLOBAL_t']]],
  ['ext_5fstart_5fmode',['ext_start_mode',['../struct_x_m_c___h_r_p_w_m___c_s_g___s_g_e_n__t.html#a8095b0d8a6e49f93b1350f1f82a0cf97',1,'XMC_HRPWM_CSG_SGEN_t']]],
  ['ext_5fstop_5fmode',['ext_stop_mode',['../struct_x_m_c___h_r_p_w_m___c_s_g___s_g_e_n__t.html#aa0e29797fe04d022d8f8f98c71ce89b2',1,'XMC_HRPWM_CSG_SGEN_t']]],
  ['extended_5fstatus',['extended_status',['../struct_x_m_c___e_t_h___m_a_c___d_m_a___d_e_s_c__t.html#a09e2225b0929efc27e0fcc87d6772c1b',1,'XMC_ETH_MAC_DMA_DESC_t']]],
  ['external_5ferror_5fenable',['external_error_enable',['../struct_x_m_c___p_o_s_i_f___h_s_c___c_o_n_f_i_g__t.html#a08605278e2711066a995f57b1da47d1d',1,'XMC_POSIF_HSC_CONFIG_t']]],
  ['external_5ferror_5flevel',['external_error_level',['../struct_x_m_c___p_o_s_i_f___h_s_c___c_o_n_f_i_g__t.html#a6d93920f43ca1c47f774ba8f3cfcdd07',1,'XMC_POSIF_HSC_CONFIG_t']]],
  ['external_5ferror_5fport',['external_error_port',['../struct_x_m_c___p_o_s_i_f___h_s_c___c_o_n_f_i_g__t.html#af5d0739130bd3f50b15b26b0ec8c4445',1,'XMC_POSIF_HSC_CONFIG_t']]],
  ['external_5fpullup',['external_pullup',['../struct_x_m_c___l_e_d_t_s___t_s___c_o_n_f_i_g___a_d_v_a_n_c_e_d__t.html#a4ecf672a443e50cb8e13785cb3e85852',1,'XMC_LEDTS_TS_CONFIG_ADVANCED_t']]],
  ['external_5ftrigger',['external_trigger',['../struct_x_m_c___v_a_d_c___s_c_a_n___c_o_n_f_i_g__t.html#a0a364133ff7f3b087ce66910e7571c4b',1,'XMC_VADC_SCAN_CONFIG_t::external_trigger()'],['../struct_x_m_c___v_a_d_c___q_u_e_u_e___e_n_t_r_y__t.html#a0a364133ff7f3b087ce66910e7571c4b',1,'XMC_VADC_QUEUE_ENTRY_t::external_trigger()'],['../struct_x_m_c___v_a_d_c___q_u_e_u_e___c_o_n_f_i_g__t.html#a0a364133ff7f3b087ce66910e7571c4b',1,'XMC_VADC_QUEUE_CONFIG_t::external_trigger()']]]
];

var searchData=
[
  ['active',['active',['../struct_x_m_c___u_s_b_d___s_t_a_t_e__t.html#ab22b96a3efad48f5a542f46c1b224800',1,'XMC_USBD_STATE_t']]],
  ['address',['address',['../struct_x_m_c___e_t_h___m_a_c__t.html#ab6b52376c1b9d81e6ea6f95a1225f832',1,'XMC_ETH_MAC_t::address()'],['../struct_x_m_c___i2_c___c_h___c_o_n_f_i_g__t.html#a643d30df67a67c5915fde941c934f9f8',1,'XMC_I2C_CH_CONFIG_t::address()'],['../struct_x_m_c___u_s_b_d___e_p__t.html#ac0d31ca829f934cccd89f8054e02773e',1,'XMC_USBD_EP_t::address()']]],
  ['address_5fcycles',['address_cycles',['../struct_x_m_c___e_b_u___b_u_s___r_e_a_d___c_o_n_f_i_g__t.html#aa5fc04645a02b6ce88faf4f57f037234',1,'XMC_EBU_BUS_READ_CONFIG_t::address_cycles()'],['../struct_x_m_c___e_b_u___b_u_s___w_r_i_t_e___c_o_n_f_i_g__t.html#aa5fc04645a02b6ce88faf4f57f037234',1,'XMC_EBU_BUS_WRITE_CONFIG_t::address_cycles()']]],
  ['address_5fhold_5fcycles',['address_hold_cycles',['../struct_x_m_c___e_b_u___b_u_s___r_e_a_d___c_o_n_f_i_g__t.html#aafea8fcb570ed737119ff6e02685fa8e',1,'XMC_EBU_BUS_READ_CONFIG_t::address_hold_cycles()'],['../struct_x_m_c___e_b_u___b_u_s___w_r_i_t_e___c_o_n_f_i_g__t.html#aafea8fcb570ed737119ff6e02685fa8e',1,'XMC_EBU_BUS_WRITE_CONFIG_t::address_hold_cycles()']]],
  ['address_5fpins_5fgpio',['address_pins_gpio',['../struct_x_m_c___e_b_u___f_r_e_e___p_i_n_s___t_o___g_p_i_o__t.html#ad6eec046d8093c6408ecbc5dac8bdea4',1,'XMC_EBU_FREE_PINS_TO_GPIO_t']]],
  ['adv_5fpin_5fgpio',['adv_pin_gpio',['../struct_x_m_c___e_b_u___f_r_e_e___p_i_n_s___t_o___g_p_i_o__t.html#a10d111ffa78011e460b2ccdc59e23da2',1,'XMC_EBU_FREE_PINS_TO_GPIO_t']]],
  ['alias_5fchannel',['alias_channel',['../struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t.html#a8a131e0b7795d896bed92b0b774a64eb',1,'XMC_VADC_CHANNEL_CONFIG_t']]],
  ['alternate_5freference',['alternate_reference',['../struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t.html#abc694c4a46e96fa73702976d69b0619f',1,'XMC_VADC_CHANNEL_CONFIG_t']]],
  ['analog_5fclock_5fdivider',['analog_clock_divider',['../struct_x_m_c___v_a_d_c___g_l_o_b_a_l___c_l_o_c_k__t.html#ad6543a1d55b9138268a4f499940b8310',1,'XMC_VADC_GLOBAL_CLOCK_t']]],
  ['api',['api',['../struct_x_m_c___u_s_b_h___d_r_i_v_e_r___v_e_r_s_i_o_n__t.html#ad180da20fbde1d3dafc074af87c19540',1,'XMC_USBH_DRIVER_VERSION_t']]],
  ['arbiter_5fclock_5fdivider',['arbiter_clock_divider',['../struct_x_m_c___v_a_d_c___g_l_o_b_a_l___c_l_o_c_k__t.html#a850df74e63baeb55a064361fe3729a34',1,'XMC_VADC_GLOBAL_CLOCK_t']]],
  ['arbiter_5fmode',['arbiter_mode',['../struct_x_m_c___v_a_d_c___g_r_o_u_p___c_o_n_f_i_g__t.html#a036485109eb610432834b802fe41091e',1,'XMC_VADC_GROUP_CONFIG_t']]],
  ['arbitration_5fround_5flength',['arbitration_round_length',['../struct_x_m_c___v_a_d_c___g_r_o_u_p___c_o_n_f_i_g__t.html#af488a892a083f03bec1fb340dc7d999d',1,'XMC_VADC_GROUP_CONFIG_t']]],
  ['asymmetric_5fpwm',['asymmetric_pwm',['../struct_x_m_c___c_c_u8___s_l_i_c_e___c_o_m_p_a_r_e___c_o_n_f_i_g__t.html#a528225f882cc14ce439e3a26823eca2e',1,'XMC_CCU8_SLICE_COMPARE_CONFIG_t']]],
  ['auto_5fsplit',['auto_split',['../struct_x_m_c___u_s_b_h___c_a_p_a_b_i_l_i_t_i_e_s__t.html#a37eab684b9a8aa496bfec9fede42fe27',1,'XMC_USBH_CAPABILITIES_t']]],
  ['autoscan_5fsynchronization',['autoscan_synchronization',['../struct_x_m_c___l_e_d_t_s___g_l_o_b_a_l___c_o_n_f_i_g__t.html#abb6d71ecc8375e01bfb440af42bc6b59',1,'XMC_LEDTS_GLOBAL_CONFIG_t']]],
  ['aux',['aux',['../struct_x_m_c___d_s_d___c_h___c_o_n_f_i_g__t.html#ad94b1161b2558c3d34aa1b93f8a7e52a',1,'XMC_DSD_CH_CONFIG_t']]]
];

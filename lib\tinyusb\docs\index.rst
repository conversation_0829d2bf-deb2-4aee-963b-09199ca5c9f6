:hide-toc:

*********
TinyUSB
*********

TinyUSB is an open-source cross-platform USB Host/Device stack for embedded systems,
designed to be memory-safe with no dynamic allocation and thread-safe with all interrupt events being deferred and then handled in the non-ISR task function.


.. toctree::
   :caption: Index
   :hidden:

   Info <info/index>
   Reference <reference/index>
   Contributing <contributing/index>

.. toctree::
   :caption: External Links
   :hidden:

   Source Code <https://github.com/hathach/tinyusb>
   Issue Tracker <https://github.com/hathach/tinyusb/issues>
   Discussions <https://github.com/hathach/tinyusb/discussions>

name: Build RISC-V

on:
  pull_request:
  push:
  release:
    types:
      - created

jobs:
  build-riscv:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        family:
        # Alphabetical order
        - 'fomu'
        - 'gd32vf103'
    steps:
    - name: Setup Python
      uses: actions/setup-python@v2

    - name: Checkout TinyUSB
      uses: actions/checkout@v2

    - name: Checkout common submodules in lib
      run: git submodule update --init lib/FreeRTOS-Kernel lib/lwip

    - name: Checkout hathach/linkermap
      uses: actions/checkout@v2
      with:
         repository: hathach/linkermap
         path: linkermap

    - name: Set Toolchain URL
      run: echo >> $GITHUB_ENV TOOLCHAIN_URL=https://github.com/xpack-dev-tools/riscv-none-embed-gcc-xpack/releases/download/v10.1.0-1.1/xpack-riscv-none-embed-gcc-10.1.0-1.1-linux-x64.tar.gz

    - name: <PERSON><PERSON>l<PERSON>in
      uses: actions/cache@v2
      id: cache-toolchain
      with:
        path: ~/cache/
        key: ${{ runner.os }}-21-03-04-${{ env.TOOLCHAIN_URL }}

    - name: Install Toolchain
      if: steps.cache-toolchain.outputs.cache-hit != 'true'
      run: |
        mkdir -p ~/cache/toolchain
        wget --progress=dot:mega $TOOLCHAIN_URL -O toolchain.tar.gz
        tar -C ~/cache/toolchain -xaf toolchain.tar.gz

    - name: Set Toolchain Path
      run: echo >> $GITHUB_PATH `echo ~/cache/toolchain/*/bin`

    - name: Build
      run: python3 tools/build_family.py ${{ matrix.family }}

    - name: Linker Map
      run: |
        pip install linkermap/
        for ex in `ls -d examples/device/*/`; do \
          find ${ex} -name *.map -print -quit | \
          xargs -I % sh -c 'echo "::group::%"; linkermap -v %; echo "::endgroup::"'; \
        done

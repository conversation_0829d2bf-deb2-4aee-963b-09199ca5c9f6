<?xml version='1.0' encoding='UTF-8' standalone='no'?>
<doxygen xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="compound.xsd" version="1.8.15">
  <compounddef id="struct_x_m_c___w_d_t___c_o_n_f_i_g__t" kind="struct" language="C++" prot="public">
    <compoundname>XMC_WDT_CONFIG_t</compoundname>
    <includes local="no">xmc_wdt.h</includes>
      <sectiondef kind="public-attrib">
      <memberdef kind="variable" id="struct_x_m_c___w_d_t___c_o_n_f_i_g__t_1ad6cd70d70f074f77af0b6a41fa0698f3" prot="public" static="no" mutable="no">
        <type>union XMC_WDT_CONFIG_t::@237</type>
        <definition>union XMC_WDT_CONFIG_t::@237 @238</definition>
        <argsstring></argsstring>
        <name>@238</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_wdt.h" line="159" column="1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___w_d_t___c_o_n_f_i_g__t_1a3e57c2ef1c3ffb36722f000cc1156824" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t __pad0__</definition>
        <argsstring></argsstring>
        <name>__pad0__</name>
        <bitfield> 1</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_wdt.h" line="147" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_wdt.h" bodystart="147" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___w_d_t___c_o_n_f_i_g__t_1a6712ba6dd1d5b43d2d56ff8ac4e275a7" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t __pad1__</definition>
        <argsstring></argsstring>
        <name>__pad1__</name>
        <bitfield> 2</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_wdt.h" line="149" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_wdt.h" bodystart="149" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___w_d_t___c_o_n_f_i_g__t_1a9ce12a63de64ef64ae2d59d128251cae" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t __pad2__</definition>
        <argsstring></argsstring>
        <name>__pad2__</name>
        <bitfield> 3</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_wdt.h" line="151" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_wdt.h" bodystart="151" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___w_d_t___c_o_n_f_i_g__t_1a4854608c0e776f0704a4d9a4b98ea57d" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t __pad3__</definition>
        <argsstring></argsstring>
        <name>__pad3__</name>
        <bitfield> 16</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_wdt.h" line="155" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_wdt.h" bodystart="155" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___w_d_t___c_o_n_f_i_g__t_1a3282745b032a6bfb98021a72893c022e" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t prewarn_mode</definition>
        <argsstring></argsstring>
        <name>prewarn_mode</name>
        <bitfield> 1</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>Pre-warning mode (PRE). This accepts boolean values as input. </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_wdt.h" line="148" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_wdt.h" bodystart="148" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___w_d_t___c_o_n_f_i_g__t_1a520b4ae1c029f1505fdb195b09fd68aa" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t run_in_debug_mode</definition>
        <argsstring></argsstring>
        <name>run_in_debug_mode</name>
        <bitfield> 1</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>Watchdog timer behaviour during debug (DSP). This accepts boolean values as input. </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_wdt.h" line="150" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_wdt.h" bodystart="150" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___w_d_t___c_o_n_f_i_g__t_1a4517f0b60aa2d2e5147db99d20adb28f" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t service_pulse_width</definition>
        <argsstring></argsstring>
        <name>service_pulse_width</name>
        <bitfield> 8</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>Service Indication Pulse Width (SPW). Generated Pulse width is of (SPW+1), in fwdt cycles.<linebreak/>
 Range: [0H to FFH] </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_wdt.h" line="152" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_wdt.h" bodystart="152" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___w_d_t___c_o_n_f_i_g__t_1aeac84de094486b36a34f500f5b16f3c1" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t wdt_ctr</definition>
        <argsstring></argsstring>
        <name>wdt_ctr</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_wdt.h" line="157" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_wdt.h" bodystart="157" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___w_d_t___c_o_n_f_i_g__t_1a880a5d11ae52f7279e36a31270eb8f9f" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t window_lower_bound</definition>
        <argsstring></argsstring>
        <name>window_lower_bound</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>Lower bound for servicing window (WLB). Setting the lower bound to 0H disables the window mechanism.<linebreak/>
Range: [0H to FFFFFFFFH] </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_wdt.h" line="140" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_wdt.h" bodystart="140" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___w_d_t___c_o_n_f_i_g__t_1aff074f1259e6fd21380c7bd67bde38ff" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t window_upper_bound</definition>
        <argsstring></argsstring>
        <name>window_upper_bound</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>Upper bound for service window (WUB). Reset request is generated up on overflow of timer. ALways upper bound value has to be more than lower bound value. If it is set lower than WLB, triggers a system reset after timer crossed upper bound value.<linebreak/>
Range: [0H to FFFFFFFFH] </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_wdt.h" line="136" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_wdt.h" bodystart="136" bodyend="-1"/>
      </memberdef>
      </sectiondef>
    <briefdescription>
    </briefdescription>
    <detaileddescription>
<para>Structure for initializing watchdog timer. Use type <ref refid="struct_x_m_c___w_d_t___c_o_n_f_i_g__t" kindref="compound">XMC_WDT_CONFIG_t</ref> for this structure. </para>
    </detaileddescription>
    <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_wdt.h" line="135" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_wdt.h" bodystart="134" bodyend="160"/>
    <listofallmembers>
      <member refid="struct_x_m_c___w_d_t___c_o_n_f_i_g__t_1a3e57c2ef1c3ffb36722f000cc1156824" prot="public" virt="non-virtual"><scope>XMC_WDT_CONFIG_t</scope><name>__pad0__</name></member>
      <member refid="struct_x_m_c___w_d_t___c_o_n_f_i_g__t_1a6712ba6dd1d5b43d2d56ff8ac4e275a7" prot="public" virt="non-virtual"><scope>XMC_WDT_CONFIG_t</scope><name>__pad1__</name></member>
      <member refid="struct_x_m_c___w_d_t___c_o_n_f_i_g__t_1a9ce12a63de64ef64ae2d59d128251cae" prot="public" virt="non-virtual"><scope>XMC_WDT_CONFIG_t</scope><name>__pad2__</name></member>
      <member refid="struct_x_m_c___w_d_t___c_o_n_f_i_g__t_1a4854608c0e776f0704a4d9a4b98ea57d" prot="public" virt="non-virtual"><scope>XMC_WDT_CONFIG_t</scope><name>__pad3__</name></member>
      <member refid="struct_x_m_c___w_d_t___c_o_n_f_i_g__t_1a3282745b032a6bfb98021a72893c022e" prot="public" virt="non-virtual"><scope>XMC_WDT_CONFIG_t</scope><name>prewarn_mode</name></member>
      <member refid="struct_x_m_c___w_d_t___c_o_n_f_i_g__t_1a520b4ae1c029f1505fdb195b09fd68aa" prot="public" virt="non-virtual"><scope>XMC_WDT_CONFIG_t</scope><name>run_in_debug_mode</name></member>
      <member refid="struct_x_m_c___w_d_t___c_o_n_f_i_g__t_1a4517f0b60aa2d2e5147db99d20adb28f" prot="public" virt="non-virtual"><scope>XMC_WDT_CONFIG_t</scope><name>service_pulse_width</name></member>
      <member refid="struct_x_m_c___w_d_t___c_o_n_f_i_g__t_1aeac84de094486b36a34f500f5b16f3c1" prot="public" virt="non-virtual"><scope>XMC_WDT_CONFIG_t</scope><name>wdt_ctr</name></member>
      <member refid="struct_x_m_c___w_d_t___c_o_n_f_i_g__t_1a880a5d11ae52f7279e36a31270eb8f9f" prot="public" virt="non-virtual"><scope>XMC_WDT_CONFIG_t</scope><name>window_lower_bound</name></member>
      <member refid="struct_x_m_c___w_d_t___c_o_n_f_i_g__t_1aff074f1259e6fd21380c7bd67bde38ff" prot="public" virt="non-virtual"><scope>XMC_WDT_CONFIG_t</scope><name>window_upper_bound</name></member>
    </listofallmembers>
  </compounddef>
</doxygen>

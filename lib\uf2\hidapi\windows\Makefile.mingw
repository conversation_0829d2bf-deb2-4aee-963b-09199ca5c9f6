###########################################
# Simple Makefile for HIDAPI test program
#
# <PERSON>
# Signal 11 Software
# 2010-06-01
###########################################

all: hidtest libhidapi.dll

CC=gcc
CXX=g++
COBJS=hid.o
CPPOBJS=../hidtest/hidtest.o
OBJS=$(COBJS) $(CPPOBJS)
CFLAGS=-I../hidapi -g -c
LIBS= -lsetupapi
DLL_LDFLAGS = -mwindows -lsetupapi

hidtest: $(OBJS)
	g++ -g $^ $(LIBS) -o hidtest

libhidapi.dll: $(OBJS)
	$(CC) -g $^ $(DLL_LDFLAGS) -o libhidapi.dll

$(COBJS): %.o: %.c
	$(CC) $(CFLAGS) $< -o $@

$(CPPOBJS): %.o: %.cpp
	$(CXX) $(CFLAGS) $< -o $@

clean:
	rm *.o ../hidtest/*.o hidtest.exe

.PHONY: clean

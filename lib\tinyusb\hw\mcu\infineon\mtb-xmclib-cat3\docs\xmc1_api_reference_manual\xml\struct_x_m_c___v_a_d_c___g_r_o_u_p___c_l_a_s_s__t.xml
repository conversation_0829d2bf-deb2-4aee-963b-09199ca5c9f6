<?xml version='1.0' encoding='UTF-8' standalone='no'?>
<doxygen xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="compound.xsd" version="1.8.15">
  <compounddef id="struct_x_m_c___v_a_d_c___g_r_o_u_p___c_l_a_s_s__t" kind="struct" language="C++" prot="public">
    <compoundname>XMC_VADC_GROUP_CLASS_t</compoundname>
    <includes local="no">xmc_vadc.h</includes>
      <sectiondef kind="public-attrib">
      <memberdef kind="variable" id="struct_x_m_c___v_a_d_c___g_r_o_u_p___c_l_a_s_s__t_1a9f6f13d45747e14931850f73da6ad596" prot="public" static="no" mutable="no">
        <type>union XMC_VADC_GROUP_CLASS_t::@205</type>
        <definition>union XMC_VADC_GROUP_CLASS_t::@205 @206</definition>
        <argsstring></argsstring>
        <name>@206</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" line="1156" column="1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___v_a_d_c___g_r_o_u_p___c_l_a_s_s__t_1a3e57c2ef1c3ffb36722f000cc1156824" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t __pad0__</definition>
        <argsstring></argsstring>
        <name>__pad0__</name>
        <bitfield> 3</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" line="1144" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" bodystart="1144" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___v_a_d_c___g_r_o_u_p___c_l_a_s_s__t_1a6712ba6dd1d5b43d2d56ff8ac4e275a7" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t __pad1__</definition>
        <argsstring></argsstring>
        <name>__pad1__</name>
        <bitfield> 5</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" line="1147" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" bodystart="1147" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___v_a_d_c___g_r_o_u_p___c_l_a_s_s__t_1a9ce12a63de64ef64ae2d59d128251cae" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t __pad2__</definition>
        <argsstring></argsstring>
        <name>__pad2__</name>
        <bitfield> 3</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" line="1150" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" bodystart="1150" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___v_a_d_c___g_r_o_u_p___c_l_a_s_s__t_1a4854608c0e776f0704a4d9a4b98ea57d" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t __pad3__</definition>
        <argsstring></argsstring>
        <name>__pad3__</name>
        <bitfield> 5</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" line="1153" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" bodystart="1153" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___v_a_d_c___g_r_o_u_p___c_l_a_s_s__t_1a01eeeee4da2b1422e19d7fd892051d0d" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t conversion_mode_emux</definition>
        <argsstring></argsstring>
        <name>conversion_mode_emux</name>
        <bitfield> 3</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>Conversion mode for channels connected via EMUX to VADC. Uses <ref refid="group___v_a_d_c_1ga9f7e6983d71750230e15684a2dc0cf12" kindref="member">XMC_VADC_CONVMODE_t</ref> </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" line="1151" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" bodystart="1151" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___v_a_d_c___g_r_o_u_p___c_l_a_s_s__t_1a85d7d0d259905d2055aea8c60d40322e" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t conversion_mode_standard</definition>
        <argsstring></argsstring>
        <name>conversion_mode_standard</name>
        <bitfield> 3</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>Conversion mode for channels directly connected to VADC. Uses <ref refid="group___v_a_d_c_1ga9f7e6983d71750230e15684a2dc0cf12" kindref="member">XMC_VADC_CONVMODE_t</ref> </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" line="1145" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" bodystart="1145" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___v_a_d_c___g_r_o_u_p___c_l_a_s_s__t_1a59bc4ee64c00c86e20955d9c2027e99e" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t g_iclass0</definition>
        <argsstring></argsstring>
        <name>g_iclass0</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" line="1155" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" bodystart="1155" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___v_a_d_c___g_r_o_u_p___c_l_a_s_s__t_1a1c640c0b3e426866078c9c727636c3b8" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t sample_time_std_conv</definition>
        <argsstring></argsstring>
        <name>sample_time_std_conv</name>
        <bitfield> 5</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>Sample time for channels directly connected to VADC <linebreak/>
Range: [0x0 to 0x1F] </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" line="1142" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" bodystart="1142" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___v_a_d_c___g_r_o_u_p___c_l_a_s_s__t_1a7e20447b6f896dd67c7a8424fd376fb1" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t sampling_phase_emux_channel</definition>
        <argsstring></argsstring>
        <name>sampling_phase_emux_channel</name>
        <bitfield> 5</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>Sample time for channels connected via EMUX <linebreak/>
Range: [0x0 to 0x1F] </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" line="1148" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" bodystart="1148" bodyend="-1"/>
      </memberdef>
      </sectiondef>
    <briefdescription>
    </briefdescription>
    <detaileddescription>
<para>Structure to initialize the group input class configuration. Configured parameters are sample time and conversion Mode. </para>
    </detaileddescription>
    <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" line="1137" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" bodystart="1136" bodyend="1157"/>
    <listofallmembers>
      <member refid="struct_x_m_c___v_a_d_c___g_r_o_u_p___c_l_a_s_s__t_1a3e57c2ef1c3ffb36722f000cc1156824" prot="public" virt="non-virtual"><scope>XMC_VADC_GROUP_CLASS_t</scope><name>__pad0__</name></member>
      <member refid="struct_x_m_c___v_a_d_c___g_r_o_u_p___c_l_a_s_s__t_1a6712ba6dd1d5b43d2d56ff8ac4e275a7" prot="public" virt="non-virtual"><scope>XMC_VADC_GROUP_CLASS_t</scope><name>__pad1__</name></member>
      <member refid="struct_x_m_c___v_a_d_c___g_r_o_u_p___c_l_a_s_s__t_1a9ce12a63de64ef64ae2d59d128251cae" prot="public" virt="non-virtual"><scope>XMC_VADC_GROUP_CLASS_t</scope><name>__pad2__</name></member>
      <member refid="struct_x_m_c___v_a_d_c___g_r_o_u_p___c_l_a_s_s__t_1a4854608c0e776f0704a4d9a4b98ea57d" prot="public" virt="non-virtual"><scope>XMC_VADC_GROUP_CLASS_t</scope><name>__pad3__</name></member>
      <member refid="struct_x_m_c___v_a_d_c___g_r_o_u_p___c_l_a_s_s__t_1a01eeeee4da2b1422e19d7fd892051d0d" prot="public" virt="non-virtual"><scope>XMC_VADC_GROUP_CLASS_t</scope><name>conversion_mode_emux</name></member>
      <member refid="struct_x_m_c___v_a_d_c___g_r_o_u_p___c_l_a_s_s__t_1a85d7d0d259905d2055aea8c60d40322e" prot="public" virt="non-virtual"><scope>XMC_VADC_GROUP_CLASS_t</scope><name>conversion_mode_standard</name></member>
      <member refid="struct_x_m_c___v_a_d_c___g_r_o_u_p___c_l_a_s_s__t_1a59bc4ee64c00c86e20955d9c2027e99e" prot="public" virt="non-virtual"><scope>XMC_VADC_GROUP_CLASS_t</scope><name>g_iclass0</name></member>
      <member refid="struct_x_m_c___v_a_d_c___g_r_o_u_p___c_l_a_s_s__t_1a1c640c0b3e426866078c9c727636c3b8" prot="public" virt="non-virtual"><scope>XMC_VADC_GROUP_CLASS_t</scope><name>sample_time_std_conv</name></member>
      <member refid="struct_x_m_c___v_a_d_c___g_r_o_u_p___c_l_a_s_s__t_1a7e20447b6f896dd67c7a8424fd376fb1" prot="public" virt="non-virtual"><scope>XMC_VADC_GROUP_CLASS_t</scope><name>sampling_phase_emux_channel</name></member>
    </listofallmembers>
  </compounddef>
</doxygen>

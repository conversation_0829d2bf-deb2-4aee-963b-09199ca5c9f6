name: Build AArch64

on:
  pull_request:
  push:
  release:
    types:
      - created

jobs:
  # ---------------------------------------
  # Build AARCH64 family
  # ---------------------------------------
  build-arm:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        family:
        # Alphabetical order
        - 'raspberrypi4'
    steps:
    - name: Setup Python
      uses: actions/setup-python@v2

    - name: Checkout TinyUSB
      uses: actions/checkout@v2

    - name: Checkout common submodules in lib
      run: git submodule update --init lib/FreeRTOS-Kernel lib/lwip lib/sct_neopixel

    - name: Checkout hathach/linkermap
      uses: actions/checkout@v2
      with:
         repository: hathach/linkermap
         path: linkermap

    - name: Set Toolchain URL
      run: echo >> $GITHUB_ENV TOOLCHAIN_URL=https://developer.arm.com/-/media/Files/downloads/gnu-a/10.3-2021.07/binrel/gcc-arm-10.3-2021.07-x86_64-aarch64-none-elf.tar.xz

    - name: Cache Toolchain
      uses: actions/cache@v2
      id: cache-toolchain
      with:
        path: ~/cache/
        key: ${{ runner.os }}-21-11-02-${{ env.TOOLCHAIN_URL }}

    - name: Install Toolchain
      if: steps.cache-toolchain.outputs.cache-hit != 'true'
      run: |
        mkdir -p ~/cache/toolchain
        wget --progress=dot:mega $TOOLCHAIN_URL -O toolchain.tar.gz
        tar -C ~/cache/toolchain -xaf toolchain.tar.gz

    - name: Set Toolchain Path
      run: echo >> $GITHUB_PATH `echo ~/cache/toolchain/*/bin`

    - name: Build
      run: python3 tools/build_family.py ${{ matrix.family }}

    - name: Linker Map
      run: |
        pip install linkermap/
        for ex in `ls -d examples/device/*/`; do \
          find ${ex} -name *.map -print -quit | \
          xargs -I % sh -c 'echo "::group::%"; linkermap -v %; echo "::endgroup::"'; \
        done

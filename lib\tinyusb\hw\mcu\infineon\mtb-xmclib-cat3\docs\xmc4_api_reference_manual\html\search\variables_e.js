var searchData=
[
  ['offset',['offset',['../struct_x_m_c___d_s_d___c_h___f_i_l_t_e_r___c_o_n_f_i_g__t.html#a59f874d10bf763992bd4b427fdb7f0cd',1,'XMC_DSD_CH_FILTER_CONFIG_t']]],
  ['omr',['OMR',['../struct_x_m_c___g_p_i_o___p_o_r_t__t.html#a88c96396fe255aecb1ccc63d88e4bc27',1,'XMC_GPIO_PORT_t']]],
  ['out',['OUT',['../struct_x_m_c___g_p_i_o___p_o_r_t__t.html#a2ab2b086280d99c3849902774755e7ea',1,'XMC_GPIO_PORT_t']]],
  ['outbuffer',['outBuffer',['../struct_x_m_c___u_s_b_d___e_p__t.html#a408bb9688f91c83741c2f07ed3d50d66',1,'XMC_USBD_EP_t']]],
  ['outbuffersize',['outBufferSize',['../struct_x_m_c___u_s_b_d___e_p__t.html#a0589fd8808073ba7e096d51488962ec7',1,'XMC_USBD_EP_t']]],
  ['outbytesavailable',['outBytesAvailable',['../struct_x_m_c___u_s_b_d___e_p__t.html#aa6458dee9393432a46d6c9f369ba8d35',1,'XMC_USBD_EP_t']]],
  ['outdr',['OUTDR',['../struct_x_m_c___u_s_i_c___c_h__t.html#afe4baa5147bfc07118e7f206c90f5ddb',1,'XMC_USIC_CH_t']]],
  ['outinuse',['outInUse',['../struct_x_m_c___u_s_b_d___e_p__t.html#a4bfb3b9bd68ad2900c933c09e95cf02d',1,'XMC_USBD_EP_t']]],
  ['outoffset',['outOffset',['../struct_x_m_c___u_s_b_d___e_p__t.html#a0633e68aa51d01355435f18ca3acb751',1,'XMC_USBD_EP_t']]],
  ['output_5flevel',['output_level',['../struct_x_m_c___g_p_i_o___c_o_n_f_i_g__t.html#a4b0a44f35dff9e6923d944c2b5fbbdf1',1,'XMC_GPIO_CONFIG_t']]],
  ['output_5fnegation',['output_negation',['../struct_x_m_c___d_a_c___c_h___c_o_n_f_i_g__t.html#ada18018e233cbe1ed86ee23e9d1146d5',1,'XMC_DAC_CH_CONFIG_t']]],
  ['output_5foffset',['output_offset',['../struct_x_m_c___d_a_c___c_h___c_o_n_f_i_g__t.html#a7d829002ac890bf222cde950b8f8420d',1,'XMC_DAC_CH_CONFIG_t']]],
  ['output_5fscale',['output_scale',['../struct_x_m_c___d_a_c___c_h___c_o_n_f_i_g__t.html#ae669ee30a6ff1ec51dba7eab152c6c05',1,'XMC_DAC_CH_CONFIG_t']]],
  ['output_5fstrength',['output_strength',['../struct_x_m_c___g_p_i_o___c_o_n_f_i_g__t.html#af4c8c95be0b5ed659d1a2d9366b5fc94',1,'XMC_GPIO_CONFIG_t']]],
  ['output_5ftrigger_5fchannel',['output_trigger_channel',['../struct_x_m_c___e_r_u___e_t_l___c_o_n_f_i_g__t.html#a40c1bf8bdc6ef232e976990848896b83',1,'XMC_ERU_ETL_CONFIG_t']]],
  ['outr',['OUTR',['../struct_x_m_c___u_s_i_c___c_h__t.html#ae703f8c37a21dd534cac2e0aedf8359f',1,'XMC_USIC_CH_t']]],
  ['overcurrent',['overcurrent',['../struct_x_m_c___u_s_b_h___p_o_r_t___s_t_a_t_e__t.html#ae4b5761b8d095bee008a94856ceca46b',1,'XMC_USBH_PORT_STATE_t']]],
  ['oversampling',['oversampling',['../struct_x_m_c___u_a_r_t___c_h___c_o_n_f_i_g__t.html#af2dbbcca6b251d4672d04aa68ecdcdbd',1,'XMC_UART_CH_CONFIG_t']]]
];

<?xml version='1.0' encoding='UTF-8' standalone='no'?>
<doxygen xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="compound.xsd" version="1.8.15">
  <compounddef id="struct_x_m_c___g_p_i_o___c_o_n_f_i_g__t" kind="struct" language="C++" prot="public">
    <compoundname>XMC_GPIO_CONFIG_t</compoundname>
    <includes local="no">xmc1_gpio.h</includes>
      <sectiondef kind="public-attrib">
      <memberdef kind="variable" id="struct_x_m_c___g_p_i_o___c_o_n_f_i_g__t_1aa47b9629ad456f9bbe7290afb46bc142" prot="public" static="no" mutable="no">
        <type><ref refid="group___g_p_i_o_1ga335e88e0140aa984a2b11cc267ccd01f" kindref="member">XMC_GPIO_INPUT_HYSTERESIS_t</ref></type>
        <definition>XMC_GPIO_INPUT_HYSTERESIS_t input_hysteresis</definition>
        <argsstring></argsstring>
        <name>input_hysteresis</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>Defines input pad hysteresis of a pin </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc1_gpio.h" line="225" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc1_gpio.h" bodystart="225" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___g_p_i_o___c_o_n_f_i_g__t_1aaa3bc9ee099fd407ab2c81b04e036c0d" prot="public" static="no" mutable="no">
        <type><ref refid="group___g_p_i_o_1ga9cd340b4c59ef98d0ab5a6ccebbacc4c" kindref="member">XMC_GPIO_MODE_t</ref></type>
        <definition>XMC_GPIO_MODE_t mode</definition>
        <argsstring></argsstring>
        <name>mode</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>Defines the direction and characteristics of a pin </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc1_gpio.h" line="224" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc1_gpio.h" bodystart="224" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___g_p_i_o___c_o_n_f_i_g__t_1a4b0a44f35dff9e6923d944c2b5fbbdf1" prot="public" static="no" mutable="no">
        <type><ref refid="group___g_p_i_o_1gae796714115da2c77c076003e8ad2053f" kindref="member">XMC_GPIO_OUTPUT_LEVEL_t</ref></type>
        <definition>XMC_GPIO_OUTPUT_LEVEL_t output_level</definition>
        <argsstring></argsstring>
        <name>output_level</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>Defines output level of a pin </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc1_gpio.h" line="226" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc1_gpio.h" bodystart="226" bodyend="-1"/>
      </memberdef>
      </sectiondef>
    <briefdescription>
    </briefdescription>
    <detaileddescription>
<para>Structure initializes port pin. Use type <ref refid="struct_x_m_c___g_p_i_o___c_o_n_f_i_g__t" kindref="compound">XMC_GPIO_CONFIG_t</ref> for this structure. </para>
    </detaileddescription>
    <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc1_gpio.h" line="223" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc1_gpio.h" bodystart="222" bodyend="227"/>
    <listofallmembers>
      <member refid="struct_x_m_c___g_p_i_o___c_o_n_f_i_g__t_1aa47b9629ad456f9bbe7290afb46bc142" prot="public" virt="non-virtual"><scope>XMC_GPIO_CONFIG_t</scope><name>input_hysteresis</name></member>
      <member refid="struct_x_m_c___g_p_i_o___c_o_n_f_i_g__t_1aaa3bc9ee099fd407ab2c81b04e036c0d" prot="public" virt="non-virtual"><scope>XMC_GPIO_CONFIG_t</scope><name>mode</name></member>
      <member refid="struct_x_m_c___g_p_i_o___c_o_n_f_i_g__t_1a4b0a44f35dff9e6923d944c2b5fbbdf1" prot="public" virt="non-virtual"><scope>XMC_GPIO_CONFIG_t</scope><name>output_level</name></member>
    </listofallmembers>
  </compounddef>
</doxygen>

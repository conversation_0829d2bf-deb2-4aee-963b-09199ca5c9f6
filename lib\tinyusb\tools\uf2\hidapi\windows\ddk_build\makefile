#############################################################################
#
#               Copyright (C) Microsoft Corporation 1995, 1996
#       All Rights Reserved.
#
#       MAKEFILE for HID directory
#
#############################################################################

!IFDEF WIN95_BUILD

ROOT=..\..\..\..

VERSIONLIST = debug retail
IS_32 = TRUE
IS_SDK = TRUE
IS_PRIVATE = TRUE
IS_SDK = TRUE
IS_DDK = TRUE
WIN32 = TRUE
COMMONMKFILE = hidapi.mk

!include $(ROOT)\dev\master.mk


!ELSE

#
# DO NOT EDIT THIS FILE!!!  Edit .\sources. if you want to add a new source
# file to this component.  This file merely indirects to the real make file
# that is shared by all the driver components of the Windows NT DDK
#

!IF DEFINED(_NT_TARGET_VERSION)
!	IF $(_NT_TARGET_VERSION)>=0x501
!		INCLUDE $(NTMAKEENV)\makefile.def
!	ELSE
#               Only warn once per directory
!               INCLUDE $(NTMAKEENV)\makefile.plt
!               IF "$(BUILD_PASS)"=="PASS1"
!		    message BUILDMSG: Warning : The sample "$(MAKEDIR)" is not valid for the current OS target.
!               ENDIF
!	ENDIF
!ELSE
!	INCLUDE $(NTMAKEENV)\makefile.def
!ENDIF

!ENDIF


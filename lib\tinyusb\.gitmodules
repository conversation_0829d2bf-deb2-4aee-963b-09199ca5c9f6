[submodule "hw/mcu/nordic/nrfx"]
	path = hw/mcu/nordic/nrfx
	url = https://github.com/NordicSemiconductor/nrfx.git
[submodule "tools/uf2"]
	path = tools/uf2
	url = https://github.com/microsoft/uf2.git
[submodule "hw/mcu/sony/cxd56/spresense-exported-sdk"]
	path = hw/mcu/sony/cxd56/spresense-exported-sdk
	url = https://github.com/sonydevworld/spresense-exported-sdk.git
[submodule "hw/mcu/ti"]
	path = hw/mcu/ti
	url = https://github.com/hathach/ti_driver.git
[submodule "hw/mcu/microchip"]
	path = hw/mcu/microchip
	url = https://github.com/hathach/microchip_driver.git
[submodule "hw/mcu/nuvoton"]
	path = hw/mcu/nuvoton
	url = https://github.com/majbthrd/nuc_driver.git
[submodule "lib/lwip"]
	path = lib/lwip
	url = https://github.com/lwip-tcpip/lwip.git
[submodule "hw/mcu/st/cmsis_device_f4"]
	path = hw/mcu/st/cmsis_device_f4
	url = https://github.com/STMicroelectronics/cmsis_device_f4.git
[submodule "hw/mcu/st/stm32f4xx_hal_driver"]
	path = hw/mcu/st/stm32f4xx_hal_driver
	url = https://github.com/STMicroelectronics/stm32f4xx_hal_driver.git
[submodule "hw/mcu/st/cmsis_device_f0"]
	path = hw/mcu/st/cmsis_device_f0
	url = https://github.com/STMicroelectronics/cmsis_device_f0.git
[submodule "hw/mcu/st/stm32f0xx_hal_driver"]
	path = hw/mcu/st/stm32f0xx_hal_driver
	url = https://github.com/STMicroelectronics/stm32f0xx_hal_driver.git
[submodule "hw/mcu/st/cmsis_device_f1"]
	path = hw/mcu/st/cmsis_device_f1
	url = https://github.com/STMicroelectronics/cmsis_device_f1.git
[submodule "hw/mcu/st/stm32f1xx_hal_driver"]
	path = hw/mcu/st/stm32f1xx_hal_driver
	url = https://github.com/STMicroelectronics/stm32f1xx_hal_driver.git
[submodule "hw/mcu/st/cmsis_device_f2"]
	path = hw/mcu/st/cmsis_device_f2
	url = https://github.com/STMicroelectronics/cmsis_device_f2.git
[submodule "hw/mcu/st/stm32f2xx_hal_driver"]
	path = hw/mcu/st/stm32f2xx_hal_driver
	url = https://github.com/STMicroelectronics/stm32f2xx_hal_driver.git
[submodule "hw/mcu/st/cmsis_device_f3"]
	path = hw/mcu/st/cmsis_device_f3
	url = https://github.com/STMicroelectronics/cmsis_device_f3.git
[submodule "hw/mcu/st/stm32f3xx_hal_driver"]
	path = hw/mcu/st/stm32f3xx_hal_driver
	url = https://github.com/STMicroelectronics/stm32f3xx_hal_driver.git
[submodule "hw/mcu/st/cmsis_device_f7"]
	path = hw/mcu/st/cmsis_device_f7
	url = https://github.com/STMicroelectronics/cmsis_device_f7.git
[submodule "hw/mcu/st/stm32f7xx_hal_driver"]
	path = hw/mcu/st/stm32f7xx_hal_driver
	url = https://github.com/STMicroelectronics/stm32f7xx_hal_driver.git
[submodule "hw/mcu/st/cmsis_device_h7"]
	path = hw/mcu/st/cmsis_device_h7
	url = https://github.com/STMicroelectronics/cmsis_device_h7.git
[submodule "hw/mcu/st/stm32h7xx_hal_driver"]
	path = hw/mcu/st/stm32h7xx_hal_driver
	url = https://github.com/STMicroelectronics/stm32h7xx_hal_driver.git
[submodule "hw/mcu/st/cmsis_device_l0"]
	path = hw/mcu/st/cmsis_device_l0
	url = https://github.com/STMicroelectronics/cmsis_device_l0.git
[submodule "hw/mcu/st/stm32l0xx_hal_driver"]
	path = hw/mcu/st/stm32l0xx_hal_driver
	url = https://github.com/STMicroelectronics/stm32l0xx_hal_driver.git
[submodule "hw/mcu/st/cmsis_device_l1"]
	path = hw/mcu/st/cmsis_device_l1
	url = https://github.com/STMicroelectronics/cmsis_device_l1.git
[submodule "hw/mcu/st/stm32l1xx_hal_driver"]
	path = hw/mcu/st/stm32l1xx_hal_driver
	url = https://github.com/STMicroelectronics/stm32l1xx_hal_driver.git
[submodule "hw/mcu/st/cmsis_device_l4"]
	path = hw/mcu/st/cmsis_device_l4
	url = https://github.com/STMicroelectronics/cmsis_device_l4.git
[submodule "hw/mcu/st/stm32l4xx_hal_driver"]
	path = hw/mcu/st/stm32l4xx_hal_driver
	url = https://github.com/STMicroelectronics/stm32l4xx_hal_driver.git
[submodule "hw/mcu/st/cmsis_device_g0"]
	path = hw/mcu/st/cmsis_device_g0
	url = https://github.com/STMicroelectronics/cmsis_device_g0.git
[submodule "hw/mcu/st/stm32g0xx_hal_driver"]
	path = hw/mcu/st/stm32g0xx_hal_driver
	url = https://github.com/STMicroelectronics/stm32g0xx_hal_driver.git
[submodule "hw/mcu/st/cmsis_device_g4"]
	path = hw/mcu/st/cmsis_device_g4
	url = https://github.com/STMicroelectronics/cmsis_device_g4.git
[submodule "hw/mcu/st/stm32g4xx_hal_driver"]
	path = hw/mcu/st/stm32g4xx_hal_driver
	url = https://github.com/STMicroelectronics/stm32g4xx_hal_driver.git
[submodule "hw/mcu/st/cmsis_device_l5"]
	path = hw/mcu/st/cmsis_device_l5
	url = https://github.com/STMicroelectronics/cmsis_device_l5.git
[submodule "hw/mcu/st/stm32l5xx_hal_driver"]
	path = hw/mcu/st/stm32l5xx_hal_driver
	url = https://github.com/STMicroelectronics/stm32l5xx_hal_driver.git
[submodule "lib/sct_neopixel"]
	path = lib/sct_neopixel
	url = https://github.com/gsteiert/sct_neopixel
[submodule "lib/FreeRTOS-Kernel"]
	path = lib/FreeRTOS-Kernel
	url = https://github.com/FreeRTOS/FreeRTOS-Kernel.git
[submodule "lib/CMSIS_5"]
	path = lib/CMSIS_5
	url = https://github.com/ARM-software/CMSIS_5.git
[submodule "hw/mcu/silabs/cmsis-dfp-efm32gg12b"]
	path = hw/mcu/silabs/cmsis-dfp-efm32gg12b
	url = https://github.com/cmsis-packs/cmsis-dfp-efm32gg12b
[submodule "hw/mcu/renesas/rx"]
	path = hw/mcu/renesas/rx
	url = https://github.com/kkitayam/rx_device.git
[submodule "hw/mcu/nxp/lpcopen"]
	path = hw/mcu/nxp/lpcopen
	url = https://github.com/hathach/nxp_lpcopen.git
[submodule "hw/mcu/nxp/mcux-sdk"]
	path = hw/mcu/nxp/mcux-sdk
	url = https://github.com/NXPmicro/mcux-sdk.git
[submodule "hw/mcu/nxp/nxp_sdk"]
	path = hw/mcu/nxp/nxp_sdk
	url = https://github.com/hathach/nxp_sdk.git
[submodule "hw/mcu/gd/nuclei-sdk"]
	path = hw/mcu/gd/nuclei-sdk
	url = https://github.com/Nuclei-Software/nuclei-sdk.git
[submodule "hw/mcu/mindmotion/mm32sdk"]
	path = hw/mcu/mindmotion/mm32sdk
	url = https://github.com/hathach/mm32sdk.git
[submodule "hw/mcu/broadcom"]
	path = hw/mcu/broadcom
	url = https://github.com/adafruit/broadcom-peripherals.git
	branch = main-build
[submodule "hw/mcu/infineon/mtb-xmclib-cat3"]
	path = hw/mcu/infineon/mtb-xmclib-cat3
	url = https://github.com/Infineon/mtb-xmclib-cat3.git

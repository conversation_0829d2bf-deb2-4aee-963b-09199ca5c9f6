###################################################################################
#                                                                                 #
# NAME: meson.build                                                               #
#                                                                                 #
# AUTHOR: <PERSON>, <PERSON>, <PERSON>.                         #
# WRITTEN BY: <PERSON>.                                                    #
#                                                                                 #
# License: MIT                                                                    #
#                                                                                 #
###################################################################################

cmock_dir = include_directories('.')

cmock_lib = static_library(meson.project_name(), 
    sources: ['cmock.c'],
    dependencies: [unity_dep],
    include_directories: cmock_dir)

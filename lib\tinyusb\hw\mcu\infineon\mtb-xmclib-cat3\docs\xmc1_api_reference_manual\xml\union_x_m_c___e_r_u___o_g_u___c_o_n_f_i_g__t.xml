<?xml version='1.0' encoding='UTF-8' standalone='no'?>
<doxygen xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="compound.xsd" version="1.8.15">
  <compounddef id="union_x_m_c___e_r_u___o_g_u___c_o_n_f_i_g__t" kind="union" language="C++" prot="public">
    <compoundname>XMC_ERU_OGU_CONFIG_t</compoundname>
    <includes local="no">xmc_eru.h</includes>
      <sectiondef kind="public-attrib">
      <memberdef kind="variable" id="union_x_m_c___e_r_u___o_g_u___c_o_n_f_i_g__t_1a4c10751942f7b95eb1c36c5783a5ce5f" prot="public" static="no" mutable="no">
        <type>struct XMC_ERU_OGU_CONFIG_t::@95</type>
        <definition>struct XMC_ERU_OGU_CONFIG_t::@95 @96</definition>
        <argsstring></argsstring>
        <name>@96</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_eru.h" line="419" column="1"/>
      </memberdef>
      <memberdef kind="variable" id="union_x_m_c___e_r_u___o_g_u___c_o_n_f_i_g__t_1a3e57c2ef1c3ffb36722f000cc1156824" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t __pad0__</definition>
        <argsstring></argsstring>
        <name>__pad0__</name>
        <bitfield> 1</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_eru.h" line="411" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_eru.h" bodystart="411" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="union_x_m_c___e_r_u___o_g_u___c_o_n_f_i_g__t_1a6712ba6dd1d5b43d2d56ff8ac4e275a7" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t __pad1__</definition>
        <argsstring></argsstring>
        <name>__pad1__</name>
        <bitfield> 6</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_eru.h" line="414" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_eru.h" bodystart="414" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="union_x_m_c___e_r_u___o_g_u___c_o_n_f_i_g__t_1a9ce12a63de64ef64ae2d59d128251cae" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t __pad2__</definition>
        <argsstring></argsstring>
        <name>__pad2__</name>
        <bitfield> 16</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_eru.h" line="418" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_eru.h" bodystart="418" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="union_x_m_c___e_r_u___o_g_u___c_o_n_f_i_g__t_1abcc483ab4303eadec1b836df7d0fbcb6" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t enable_pattern_detection</definition>
        <argsstring></argsstring>
        <name>enable_pattern_detection</name>
        <bitfield> 1</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>Enable generation of(GEEN) event for pattern detection result change. This accepts boolean values as input. </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_eru.h" line="409" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_eru.h" bodystart="409" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="union_x_m_c___e_r_u___o_g_u___c_o_n_f_i_g__t_1a6b0203da525dc5fd2b284ffe9e964904" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t pattern_detection_input</definition>
        <argsstring></argsstring>
        <name>pattern_detection_input</name>
        <bitfield> 4</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>Enable input for the pattern detection(IPENx, x = [0 to 3]). Refer <ref refid="group___e_r_u_1gafe843daae3d38c529ffd50b148d61628" kindref="member">XMC_ERU_OGU_PATTERN_DETECTION_INPUT_t</ref> for valid values. <bold>OR</bold> combination of the enum items given as input </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_eru.h" line="415" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_eru.h" bodystart="415" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="union_x_m_c___e_r_u___o_g_u___c_o_n_f_i_g__t_1aaaba12878092ef18157eae0cf89bc766" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t peripheral_trigger</definition>
        <argsstring></argsstring>
        <name>peripheral_trigger</name>
        <bitfield> 2</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>peripheral trigger(ISS) input selection. Refer <ref refid="group___e_r_u_1gab2ee1dc0cb1c6218868da04976b6819a" kindref="member">XMC_ERU_OGU_PERIPHERAL_TRIGGER_t</ref> for valid values. </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_eru.h" line="407" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_eru.h" bodystart="407" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="union_x_m_c___e_r_u___o_g_u___c_o_n_f_i_g__t_1a0a595268561edc58e347ca8387000bc6" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t raw</definition>
        <argsstring></argsstring>
        <name>raw</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_eru.h" line="403" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_eru.h" bodystart="403" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="union_x_m_c___e_r_u___o_g_u___c_o_n_f_i_g__t_1af4b7148c93733c140d5b8c9d8bd9929e" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t service_request</definition>
        <argsstring></argsstring>
        <name>service_request</name>
        <bitfield> 2</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>Gating(GP) on service request generation for pattern detection result. Refer <ref refid="group___e_r_u_1gaae4af18f2e9af9ae235a3d69b4007c0e" kindref="member">XMC_ERU_OGU_SERVICE_REQUEST_t</ref> for valid values. </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_eru.h" line="412" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_eru.h" bodystart="412" bodyend="-1"/>
      </memberdef>
      </sectiondef>
    <briefdescription>
    </briefdescription>
    <detaileddescription>
<para>Structure for initializing ERUx_OGUy (x = [0], y = [0..4]) module. </para>
    </detaileddescription>
    <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_eru.h" line="402" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_eru.h" bodystart="401" bodyend="420"/>
    <listofallmembers>
      <member refid="union_x_m_c___e_r_u___o_g_u___c_o_n_f_i_g__t_1a3e57c2ef1c3ffb36722f000cc1156824" prot="public" virt="non-virtual"><scope>XMC_ERU_OGU_CONFIG_t</scope><name>__pad0__</name></member>
      <member refid="union_x_m_c___e_r_u___o_g_u___c_o_n_f_i_g__t_1a6712ba6dd1d5b43d2d56ff8ac4e275a7" prot="public" virt="non-virtual"><scope>XMC_ERU_OGU_CONFIG_t</scope><name>__pad1__</name></member>
      <member refid="union_x_m_c___e_r_u___o_g_u___c_o_n_f_i_g__t_1a9ce12a63de64ef64ae2d59d128251cae" prot="public" virt="non-virtual"><scope>XMC_ERU_OGU_CONFIG_t</scope><name>__pad2__</name></member>
      <member refid="union_x_m_c___e_r_u___o_g_u___c_o_n_f_i_g__t_1abcc483ab4303eadec1b836df7d0fbcb6" prot="public" virt="non-virtual"><scope>XMC_ERU_OGU_CONFIG_t</scope><name>enable_pattern_detection</name></member>
      <member refid="union_x_m_c___e_r_u___o_g_u___c_o_n_f_i_g__t_1a6b0203da525dc5fd2b284ffe9e964904" prot="public" virt="non-virtual"><scope>XMC_ERU_OGU_CONFIG_t</scope><name>pattern_detection_input</name></member>
      <member refid="union_x_m_c___e_r_u___o_g_u___c_o_n_f_i_g__t_1aaaba12878092ef18157eae0cf89bc766" prot="public" virt="non-virtual"><scope>XMC_ERU_OGU_CONFIG_t</scope><name>peripheral_trigger</name></member>
      <member refid="union_x_m_c___e_r_u___o_g_u___c_o_n_f_i_g__t_1a0a595268561edc58e347ca8387000bc6" prot="public" virt="non-virtual"><scope>XMC_ERU_OGU_CONFIG_t</scope><name>raw</name></member>
      <member refid="union_x_m_c___e_r_u___o_g_u___c_o_n_f_i_g__t_1af4b7148c93733c140d5b8c9d8bd9929e" prot="public" virt="non-virtual"><scope>XMC_ERU_OGU_CONFIG_t</scope><name>service_request</name></member>
    </listofallmembers>
  </compounddef>
</doxygen>

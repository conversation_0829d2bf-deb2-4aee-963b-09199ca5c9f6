<?xml version='1.0' encoding='UTF-8' standalone='no'?>
<doxygen xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="compound.xsd" version="1.8.15">
  <compounddef id="struct_x_m_c___v_a_d_c___r_e_s_u_l_t___c_o_n_f_i_g__t" kind="struct" language="C++" prot="public">
    <compoundname>XMC_VADC_RESULT_CONFIG_t</compoundname>
    <includes local="no">xmc_vadc.h</includes>
      <sectiondef kind="public-attrib">
      <memberdef kind="variable" id="struct_x_m_c___v_a_d_c___r_e_s_u_l_t___c_o_n_f_i_g__t_1aa0798f725d1b41eaf92af5848c33057a" prot="public" static="no" mutable="no">
        <type>union XMC_VADC_RESULT_CONFIG_t::@221</type>
        <definition>union XMC_VADC_RESULT_CONFIG_t::@221 @222</definition>
        <argsstring></argsstring>
        <name>@222</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" line="1254" column="1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___v_a_d_c___r_e_s_u_l_t___c_o_n_f_i_g__t_1a3e57c2ef1c3ffb36722f000cc1156824" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t __pad0__</definition>
        <argsstring></argsstring>
        <name>__pad0__</name>
        <bitfield> 16</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" line="1241" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" bodystart="1241" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___v_a_d_c___r_e_s_u_l_t___c_o_n_f_i_g__t_1a6712ba6dd1d5b43d2d56ff8ac4e275a7" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t __pad1__</definition>
        <argsstring></argsstring>
        <name>__pad1__</name>
        <bitfield> 2</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" line="1247" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" bodystart="1247" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___v_a_d_c___r_e_s_u_l_t___c_o_n_f_i_g__t_1a9ce12a63de64ef64ae2d59d128251cae" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t __pad2__</definition>
        <argsstring></argsstring>
        <name>__pad2__</name>
        <bitfield> 4</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" line="1250" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" bodystart="1250" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___v_a_d_c___r_e_s_u_l_t___c_o_n_f_i_g__t_1ab214fae777d811b0ecb538ee46d52950" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t data_reduction_control</definition>
        <argsstring></argsstring>
        <name>data_reduction_control</name>
        <bitfield> 4</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>Configures the data reduction stages </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" line="1242" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" bodystart="1242" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___v_a_d_c___r_e_s_u_l_t___c_o_n_f_i_g__t_1a8b629d8f3ceb3335a384b5f5b443ad8b" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t event_gen_enable</definition>
        <argsstring></argsstring>
        <name>event_gen_enable</name>
        <bitfield> 1</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>Generates an event on availability of new result. </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" line="1251" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" bodystart="1251" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___v_a_d_c___r_e_s_u_l_t___c_o_n_f_i_g__t_1a13bc9099250fa41dd864e4d62dca779d" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t g_rcr</definition>
        <argsstring></argsstring>
        <name>g_rcr</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" line="1253" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" bodystart="1253" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___v_a_d_c___r_e_s_u_l_t___c_o_n_f_i_g__t_1ad9578a8c2442acfa8e7718359be90ca7" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t part_of_fifo</definition>
        <argsstring></argsstring>
        <name>part_of_fifo</name>
        <bitfield> 2</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>Make the result register a part of Result FIFO? </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" line="1249" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" bodystart="1249" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___v_a_d_c___r_e_s_u_l_t___c_o_n_f_i_g__t_1ac786f3e57004bca327eced6ab517b47a" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t post_processing_mode</definition>
        <argsstring></argsstring>
        <name>post_processing_mode</name>
        <bitfield> 2</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>Result data processing mode. Uses <ref refid="group___v_a_d_c_1gaff5ca81d747517a6420fea34642c6778" kindref="member">XMC_VADC_DMM_t</ref> For normal operation select XMC_VADC_DMM_t::XMC_VADC_DMM_REDUCTION_MODE and data_reduction_control as 0 </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" line="1243" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" bodystart="1243" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___v_a_d_c___r_e_s_u_l_t___c_o_n_f_i_g__t_1af501c18c04e222f0e9d0cb0cf7b66d84" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t wait_for_read_mode</definition>
        <argsstring></argsstring>
        <name>wait_for_read_mode</name>
        <bitfield> 1</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>Allow the conversion only after previous results are read </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" line="1248" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" bodystart="1248" bodyend="-1"/>
      </memberdef>
      </sectiondef>
    <briefdescription>
    </briefdescription>
    <detaileddescription>
<para>Structure to initialize VADC Group result register. </para>
    </detaileddescription>
    <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" line="1236" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" bodystart="1235" bodyend="1255"/>
    <listofallmembers>
      <member refid="struct_x_m_c___v_a_d_c___r_e_s_u_l_t___c_o_n_f_i_g__t_1a3e57c2ef1c3ffb36722f000cc1156824" prot="public" virt="non-virtual"><scope>XMC_VADC_RESULT_CONFIG_t</scope><name>__pad0__</name></member>
      <member refid="struct_x_m_c___v_a_d_c___r_e_s_u_l_t___c_o_n_f_i_g__t_1a6712ba6dd1d5b43d2d56ff8ac4e275a7" prot="public" virt="non-virtual"><scope>XMC_VADC_RESULT_CONFIG_t</scope><name>__pad1__</name></member>
      <member refid="struct_x_m_c___v_a_d_c___r_e_s_u_l_t___c_o_n_f_i_g__t_1a9ce12a63de64ef64ae2d59d128251cae" prot="public" virt="non-virtual"><scope>XMC_VADC_RESULT_CONFIG_t</scope><name>__pad2__</name></member>
      <member refid="struct_x_m_c___v_a_d_c___r_e_s_u_l_t___c_o_n_f_i_g__t_1ab214fae777d811b0ecb538ee46d52950" prot="public" virt="non-virtual"><scope>XMC_VADC_RESULT_CONFIG_t</scope><name>data_reduction_control</name></member>
      <member refid="struct_x_m_c___v_a_d_c___r_e_s_u_l_t___c_o_n_f_i_g__t_1a8b629d8f3ceb3335a384b5f5b443ad8b" prot="public" virt="non-virtual"><scope>XMC_VADC_RESULT_CONFIG_t</scope><name>event_gen_enable</name></member>
      <member refid="struct_x_m_c___v_a_d_c___r_e_s_u_l_t___c_o_n_f_i_g__t_1a13bc9099250fa41dd864e4d62dca779d" prot="public" virt="non-virtual"><scope>XMC_VADC_RESULT_CONFIG_t</scope><name>g_rcr</name></member>
      <member refid="struct_x_m_c___v_a_d_c___r_e_s_u_l_t___c_o_n_f_i_g__t_1ad9578a8c2442acfa8e7718359be90ca7" prot="public" virt="non-virtual"><scope>XMC_VADC_RESULT_CONFIG_t</scope><name>part_of_fifo</name></member>
      <member refid="struct_x_m_c___v_a_d_c___r_e_s_u_l_t___c_o_n_f_i_g__t_1ac786f3e57004bca327eced6ab517b47a" prot="public" virt="non-virtual"><scope>XMC_VADC_RESULT_CONFIG_t</scope><name>post_processing_mode</name></member>
      <member refid="struct_x_m_c___v_a_d_c___r_e_s_u_l_t___c_o_n_f_i_g__t_1af501c18c04e222f0e9d0cb0cf7b66d84" prot="public" virt="non-virtual"><scope>XMC_VADC_RESULT_CONFIG_t</scope><name>wait_for_read_mode</name></member>
    </listofallmembers>
  </compounddef>
</doxygen>

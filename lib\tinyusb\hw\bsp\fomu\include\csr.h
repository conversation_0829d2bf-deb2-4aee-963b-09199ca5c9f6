//--------------------------------------------------------------------------------
// Auto-generated by <PERSON><PERSON> (f4fcd10) & Lite<PERSON> (1425a68d) on 2019-11-12 19:41:49
//--------------------------------------------------------------------------------
#ifndef __GENERATED_CSR_H
#define __GENERATED_CSR_H
#include <stdint.h>
#ifdef CSR_ACCESSORS_DEFINED
extern void csr_writeb(uint8_t value, unsigned long addr);
extern uint8_t csr_readb(unsigned long addr);
extern void csr_writew(uint16_t value, unsigned long addr);
extern uint16_t csr_readw(unsigned long addr);
extern void csr_writel(uint32_t value, unsigned long addr);
extern uint32_t csr_readl(unsigned long addr);
#else /* ! CSR_ACCESSORS_DEFINED */
#include <hw/common.h>
#endif /* ! CSR_ACCESSORS_DEFINED */

/* ctrl */
#define CSR_CTRL_BASE 0xe0000000L
#define CSR_CTRL_RESET_ADDR 0xe0000000L
#define CSR_CTRL_RESET_SIZE 1
static inline unsigned char ctrl_reset_read(void) {
	unsigned char r = csr_readl(0xe0000000L);
	return r;
}
static inline void ctrl_reset_write(unsigned char value) {
	csr_writel(value, 0xe0000000L);
}
#define CSR_CTRL_SCRATCH_ADDR 0xe0000004L
#define CSR_CTRL_SCRATCH_SIZE 4
static inline unsigned int ctrl_scratch_read(void) {
	unsigned int r = csr_readl(0xe0000004L);
	r <<= 8;
	r |= csr_readl(0xe0000008L);
	r <<= 8;
	r |= csr_readl(0xe000000cL);
	r <<= 8;
	r |= csr_readl(0xe0000010L);
	return r;
}
static inline void ctrl_scratch_write(unsigned int value) {
	csr_writel(value >> 24, 0xe0000004L);
	csr_writel(value >> 16, 0xe0000008L);
	csr_writel(value >> 8, 0xe000000cL);
	csr_writel(value, 0xe0000010L);
}
#define CSR_CTRL_BUS_ERRORS_ADDR 0xe0000014L
#define CSR_CTRL_BUS_ERRORS_SIZE 4
static inline unsigned int ctrl_bus_errors_read(void) {
	unsigned int r = csr_readl(0xe0000014L);
	r <<= 8;
	r |= csr_readl(0xe0000018L);
	r <<= 8;
	r |= csr_readl(0xe000001cL);
	r <<= 8;
	r |= csr_readl(0xe0000020L);
	return r;
}

/* messible */
#define CSR_MESSIBLE_BASE 0xe0008000L
#define CSR_MESSIBLE_IN_ADDR 0xe0008000L
#define CSR_MESSIBLE_IN_SIZE 1
static inline unsigned char messible_in_read(void) {
	unsigned char r = csr_readl(0xe0008000L);
	return r;
}
static inline void messible_in_write(unsigned char value) {
	csr_writel(value, 0xe0008000L);
}
#define CSR_MESSIBLE_OUT_ADDR 0xe0008004L
#define CSR_MESSIBLE_OUT_SIZE 1
static inline unsigned char messible_out_read(void) {
	unsigned char r = csr_readl(0xe0008004L);
	return r;
}
#define CSR_MESSIBLE_STATUS_ADDR 0xe0008008L
#define CSR_MESSIBLE_STATUS_SIZE 1
static inline unsigned char messible_status_read(void) {
	unsigned char r = csr_readl(0xe0008008L);
	return r;
}
#define CSR_MESSIBLE_STATUS_FULL_OFFSET 0
#define CSR_MESSIBLE_STATUS_FULL_SIZE 1
#define CSR_MESSIBLE_STATUS_HAVE_OFFSET 1
#define CSR_MESSIBLE_STATUS_HAVE_SIZE 1

/* picorvspi */
#define CSR_PICORVSPI_BASE 0xe0005000L
#define CSR_PICORVSPI_CFG1_ADDR 0xe0005000L
#define CSR_PICORVSPI_CFG1_SIZE 1
static inline unsigned char picorvspi_cfg1_read(void) {
	unsigned char r = csr_readl(0xe0005000L);
	return r;
}
static inline void picorvspi_cfg1_write(unsigned char value) {
	csr_writel(value, 0xe0005000L);
}
#define CSR_PICORVSPI_CFG1_BB_OUT_OFFSET 0
#define CSR_PICORVSPI_CFG1_BB_OUT_SIZE 4
#define CSR_PICORVSPI_CFG1_BB_CLK_OFFSET 4
#define CSR_PICORVSPI_CFG1_BB_CLK_SIZE 1
#define CSR_PICORVSPI_CFG1_BB_CS_OFFSET 5
#define CSR_PICORVSPI_CFG1_BB_CS_SIZE 1
#define CSR_PICORVSPI_CFG2_ADDR 0xe0005004L
#define CSR_PICORVSPI_CFG2_SIZE 1
static inline unsigned char picorvspi_cfg2_read(void) {
	unsigned char r = csr_readl(0xe0005004L);
	return r;
}
static inline void picorvspi_cfg2_write(unsigned char value) {
	csr_writel(value, 0xe0005004L);
}
#define CSR_PICORVSPI_CFG2_BB_OE_OFFSET 0
#define CSR_PICORVSPI_CFG2_BB_OE_SIZE 4
#define CSR_PICORVSPI_CFG3_ADDR 0xe0005008L
#define CSR_PICORVSPI_CFG3_SIZE 1
static inline unsigned char picorvspi_cfg3_read(void) {
	unsigned char r = csr_readl(0xe0005008L);
	return r;
}
static inline void picorvspi_cfg3_write(unsigned char value) {
	csr_writel(value, 0xe0005008L);
}
#define CSR_PICORVSPI_CFG3_RLAT_OFFSET 0
#define CSR_PICORVSPI_CFG3_RLAT_SIZE 4
#define CSR_PICORVSPI_CFG3_CRM_OFFSET 4
#define CSR_PICORVSPI_CFG3_CRM_SIZE 1
#define CSR_PICORVSPI_CFG3_QSPI_OFFSET 5
#define CSR_PICORVSPI_CFG3_QSPI_SIZE 1
#define CSR_PICORVSPI_CFG3_DDR_OFFSET 6
#define CSR_PICORVSPI_CFG3_DDR_SIZE 1
#define CSR_PICORVSPI_CFG4_ADDR 0xe000500cL
#define CSR_PICORVSPI_CFG4_SIZE 1
static inline unsigned char picorvspi_cfg4_read(void) {
	unsigned char r = csr_readl(0xe000500cL);
	return r;
}
static inline void picorvspi_cfg4_write(unsigned char value) {
	csr_writel(value, 0xe000500cL);
}
#define CSR_PICORVSPI_CFG4_MEMIO_OFFSET 7
#define CSR_PICORVSPI_CFG4_MEMIO_SIZE 1
#define CSR_PICORVSPI_STAT1_ADDR 0xe0005010L
#define CSR_PICORVSPI_STAT1_SIZE 1
static inline unsigned char picorvspi_stat1_read(void) {
	unsigned char r = csr_readl(0xe0005010L);
	return r;
}
#define CSR_PICORVSPI_STAT1_BB_IN_OFFSET 0
#define CSR_PICORVSPI_STAT1_BB_IN_SIZE 4
#define CSR_PICORVSPI_STAT2_ADDR 0xe0005014L
#define CSR_PICORVSPI_STAT2_SIZE 1
static inline unsigned char picorvspi_stat2_read(void) {
	unsigned char r = csr_readl(0xe0005014L);
	return r;
}
#define CSR_PICORVSPI_STAT3_ADDR 0xe0005018L
#define CSR_PICORVSPI_STAT3_SIZE 1
static inline unsigned char picorvspi_stat3_read(void) {
	unsigned char r = csr_readl(0xe0005018L);
	return r;
}
#define CSR_PICORVSPI_STAT4_ADDR 0xe000501cL
#define CSR_PICORVSPI_STAT4_SIZE 1
static inline unsigned char picorvspi_stat4_read(void) {
	unsigned char r = csr_readl(0xe000501cL);
	return r;
}

/* reboot */
#define CSR_REBOOT_BASE 0xe0006000L
#define CSR_REBOOT_CTRL_ADDR 0xe0006000L
#define CSR_REBOOT_CTRL_SIZE 1
static inline unsigned char reboot_ctrl_read(void) {
	unsigned char r = csr_readl(0xe0006000L);
	return r;
}
static inline void reboot_ctrl_write(unsigned char value) {
	csr_writel(value, 0xe0006000L);
}
#define CSR_REBOOT_CTRL_IMAGE_OFFSET 0
#define CSR_REBOOT_CTRL_IMAGE_SIZE 2
#define CSR_REBOOT_CTRL_KEY_OFFSET 2
#define CSR_REBOOT_CTRL_KEY_SIZE 6
#define CSR_REBOOT_ADDR_ADDR 0xe0006004L
#define CSR_REBOOT_ADDR_SIZE 4
static inline unsigned int reboot_addr_read(void) {
	unsigned int r = csr_readl(0xe0006004L);
	r <<= 8;
	r |= csr_readl(0xe0006008L);
	r <<= 8;
	r |= csr_readl(0xe000600cL);
	r <<= 8;
	r |= csr_readl(0xe0006010L);
	return r;
}
static inline void reboot_addr_write(unsigned int value) {
	csr_writel(value >> 24, 0xe0006004L);
	csr_writel(value >> 16, 0xe0006008L);
	csr_writel(value >> 8, 0xe000600cL);
	csr_writel(value, 0xe0006010L);
}

/* rgb */
#define CSR_RGB_BASE 0xe0006800L
#define CSR_RGB_DAT_ADDR 0xe0006800L
#define CSR_RGB_DAT_SIZE 1
static inline unsigned char rgb_dat_read(void) {
	unsigned char r = csr_readl(0xe0006800L);
	return r;
}
static inline void rgb_dat_write(unsigned char value) {
	csr_writel(value, 0xe0006800L);
}
#define CSR_RGB_ADDR_ADDR 0xe0006804L
#define CSR_RGB_ADDR_SIZE 1
static inline unsigned char rgb_addr_read(void) {
	unsigned char r = csr_readl(0xe0006804L);
	return r;
}
static inline void rgb_addr_write(unsigned char value) {
	csr_writel(value, 0xe0006804L);
}
#define CSR_RGB_CTRL_ADDR 0xe0006808L
#define CSR_RGB_CTRL_SIZE 1
static inline unsigned char rgb_ctrl_read(void) {
	unsigned char r = csr_readl(0xe0006808L);
	return r;
}
static inline void rgb_ctrl_write(unsigned char value) {
	csr_writel(value, 0xe0006808L);
}
#define CSR_RGB_CTRL_EXE_OFFSET 0
#define CSR_RGB_CTRL_EXE_SIZE 1
#define CSR_RGB_CTRL_CURREN_OFFSET 1
#define CSR_RGB_CTRL_CURREN_SIZE 1
#define CSR_RGB_CTRL_RGBLEDEN_OFFSET 2
#define CSR_RGB_CTRL_RGBLEDEN_SIZE 1
#define CSR_RGB_CTRL_RRAW_OFFSET 3
#define CSR_RGB_CTRL_RRAW_SIZE 1
#define CSR_RGB_CTRL_GRAW_OFFSET 4
#define CSR_RGB_CTRL_GRAW_SIZE 1
#define CSR_RGB_CTRL_BRAW_OFFSET 5
#define CSR_RGB_CTRL_BRAW_SIZE 1
#define CSR_RGB_RAW_ADDR 0xe000680cL
#define CSR_RGB_RAW_SIZE 1
static inline unsigned char rgb_raw_read(void) {
	unsigned char r = csr_readl(0xe000680cL);
	return r;
}
static inline void rgb_raw_write(unsigned char value) {
	csr_writel(value, 0xe000680cL);
}
#define CSR_RGB_RAW_R_OFFSET 0
#define CSR_RGB_RAW_R_SIZE 1
#define CSR_RGB_RAW_G_OFFSET 1
#define CSR_RGB_RAW_G_SIZE 1
#define CSR_RGB_RAW_B_OFFSET 2
#define CSR_RGB_RAW_B_SIZE 1

/* timer0 */
#define CSR_TIMER0_BASE 0xe0002800L
#define CSR_TIMER0_LOAD_ADDR 0xe0002800L
#define CSR_TIMER0_LOAD_SIZE 4
static inline unsigned int timer0_load_read(void) {
	unsigned int r = csr_readl(0xe0002800L);
	r <<= 8;
	r |= csr_readl(0xe0002804L);
	r <<= 8;
	r |= csr_readl(0xe0002808L);
	r <<= 8;
	r |= csr_readl(0xe000280cL);
	return r;
}
static inline void timer0_load_write(unsigned int value) {
	csr_writel(value >> 24, 0xe0002800L);
	csr_writel(value >> 16, 0xe0002804L);
	csr_writel(value >> 8, 0xe0002808L);
	csr_writel(value, 0xe000280cL);
}
#define CSR_TIMER0_RELOAD_ADDR 0xe0002810L
#define CSR_TIMER0_RELOAD_SIZE 4
static inline unsigned int timer0_reload_read(void) {
	unsigned int r = csr_readl(0xe0002810L);
	r <<= 8;
	r |= csr_readl(0xe0002814L);
	r <<= 8;
	r |= csr_readl(0xe0002818L);
	r <<= 8;
	r |= csr_readl(0xe000281cL);
	return r;
}
static inline void timer0_reload_write(unsigned int value) {
	csr_writel(value >> 24, 0xe0002810L);
	csr_writel(value >> 16, 0xe0002814L);
	csr_writel(value >> 8, 0xe0002818L);
	csr_writel(value, 0xe000281cL);
}
#define CSR_TIMER0_EN_ADDR 0xe0002820L
#define CSR_TIMER0_EN_SIZE 1
static inline unsigned char timer0_en_read(void) {
	unsigned char r = csr_readl(0xe0002820L);
	return r;
}
static inline void timer0_en_write(unsigned char value) {
	csr_writel(value, 0xe0002820L);
}
#define CSR_TIMER0_UPDATE_VALUE_ADDR 0xe0002824L
#define CSR_TIMER0_UPDATE_VALUE_SIZE 1
static inline unsigned char timer0_update_value_read(void) {
	unsigned char r = csr_readl(0xe0002824L);
	return r;
}
static inline void timer0_update_value_write(unsigned char value) {
	csr_writel(value, 0xe0002824L);
}
#define CSR_TIMER0_VALUE_ADDR 0xe0002828L
#define CSR_TIMER0_VALUE_SIZE 4
static inline unsigned int timer0_value_read(void) {
	unsigned int r = csr_readl(0xe0002828L);
	r <<= 8;
	r |= csr_readl(0xe000282cL);
	r <<= 8;
	r |= csr_readl(0xe0002830L);
	r <<= 8;
	r |= csr_readl(0xe0002834L);
	return r;
}
#define CSR_TIMER0_EV_STATUS_ADDR 0xe0002838L
#define CSR_TIMER0_EV_STATUS_SIZE 1
static inline unsigned char timer0_ev_status_read(void) {
	unsigned char r = csr_readl(0xe0002838L);
	return r;
}
static inline void timer0_ev_status_write(unsigned char value) {
	csr_writel(value, 0xe0002838L);
}
#define CSR_TIMER0_EV_PENDING_ADDR 0xe000283cL
#define CSR_TIMER0_EV_PENDING_SIZE 1
static inline unsigned char timer0_ev_pending_read(void) {
	unsigned char r = csr_readl(0xe000283cL);
	return r;
}
static inline void timer0_ev_pending_write(unsigned char value) {
	csr_writel(value, 0xe000283cL);
}
#define CSR_TIMER0_EV_ENABLE_ADDR 0xe0002840L
#define CSR_TIMER0_EV_ENABLE_SIZE 1
static inline unsigned char timer0_ev_enable_read(void) {
	unsigned char r = csr_readl(0xe0002840L);
	return r;
}
static inline void timer0_ev_enable_write(unsigned char value) {
	csr_writel(value, 0xe0002840L);
}

/* touch */
#define CSR_TOUCH_BASE 0xe0005800L
#define CSR_TOUCH_O_ADDR 0xe0005800L
#define CSR_TOUCH_O_SIZE 1
static inline unsigned char touch_o_read(void) {
	unsigned char r = csr_readl(0xe0005800L);
	return r;
}
static inline void touch_o_write(unsigned char value) {
	csr_writel(value, 0xe0005800L);
}
#define CSR_TOUCH_O_O_OFFSET 0
#define CSR_TOUCH_O_O_SIZE 4
#define CSR_TOUCH_OE_ADDR 0xe0005804L
#define CSR_TOUCH_OE_SIZE 1
static inline unsigned char touch_oe_read(void) {
	unsigned char r = csr_readl(0xe0005804L);
	return r;
}
static inline void touch_oe_write(unsigned char value) {
	csr_writel(value, 0xe0005804L);
}
#define CSR_TOUCH_OE_OE_OFFSET 0
#define CSR_TOUCH_OE_OE_SIZE 4
#define CSR_TOUCH_I_ADDR 0xe0005808L
#define CSR_TOUCH_I_SIZE 1
static inline unsigned char touch_i_read(void) {
	unsigned char r = csr_readl(0xe0005808L);
	return r;
}
#define CSR_TOUCH_I_I_OFFSET 0
#define CSR_TOUCH_I_I_SIZE 4

/* usb */
#define CSR_USB_BASE 0xe0004800L
#define CSR_USB_PULLUP_OUT_ADDR 0xe0004800L
#define CSR_USB_PULLUP_OUT_SIZE 1
static inline unsigned char usb_pullup_out_read(void) {
	unsigned char r = csr_readl(0xe0004800L);
	return r;
}
static inline void usb_pullup_out_write(unsigned char value) {
	csr_writel(value, 0xe0004800L);
}
#define CSR_USB_ADDRESS_ADDR 0xe0004804L
#define CSR_USB_ADDRESS_SIZE 1
static inline unsigned char usb_address_read(void) {
	unsigned char r = csr_readl(0xe0004804L);
	return r;
}
static inline void usb_address_write(unsigned char value) {
	csr_writel(value, 0xe0004804L);
}
#define CSR_USB_ADDRESS_ADDR_OFFSET 0
#define CSR_USB_ADDRESS_ADDR_SIZE 7
#define CSR_USB_NEXT_EV_ADDR 0xe0004808L
#define CSR_USB_NEXT_EV_SIZE 1
static inline unsigned char usb_next_ev_read(void) {
	unsigned char r = csr_readl(0xe0004808L);
	return r;
}
#define CSR_USB_NEXT_EV_IN_OFFSET 0
#define CSR_USB_NEXT_EV_IN_SIZE 1
#define CSR_USB_NEXT_EV_OUT_OFFSET 1
#define CSR_USB_NEXT_EV_OUT_SIZE 1
#define CSR_USB_NEXT_EV_SETUP_OFFSET 2
#define CSR_USB_NEXT_EV_SETUP_SIZE 1
#define CSR_USB_NEXT_EV_RESET_OFFSET 3
#define CSR_USB_NEXT_EV_RESET_SIZE 1
#define CSR_USB_SETUP_DATA_ADDR 0xe000480cL
#define CSR_USB_SETUP_DATA_SIZE 1
static inline unsigned char usb_setup_data_read(void) {
	unsigned char r = csr_readl(0xe000480cL);
	return r;
}
#define CSR_USB_SETUP_DATA_DATA_OFFSET 0
#define CSR_USB_SETUP_DATA_DATA_SIZE 8
#define CSR_USB_SETUP_CTRL_ADDR 0xe0004810L
#define CSR_USB_SETUP_CTRL_SIZE 1
static inline unsigned char usb_setup_ctrl_read(void) {
	unsigned char r = csr_readl(0xe0004810L);
	return r;
}
static inline void usb_setup_ctrl_write(unsigned char value) {
	csr_writel(value, 0xe0004810L);
}
#define CSR_USB_SETUP_CTRL_RESET_OFFSET 5
#define CSR_USB_SETUP_CTRL_RESET_SIZE 1
#define CSR_USB_SETUP_STATUS_ADDR 0xe0004814L
#define CSR_USB_SETUP_STATUS_SIZE 1
static inline unsigned char usb_setup_status_read(void) {
	unsigned char r = csr_readl(0xe0004814L);
	return r;
}
#define CSR_USB_SETUP_STATUS_EPNO_OFFSET 0
#define CSR_USB_SETUP_STATUS_EPNO_SIZE 4
#define CSR_USB_SETUP_STATUS_HAVE_OFFSET 4
#define CSR_USB_SETUP_STATUS_HAVE_SIZE 1
#define CSR_USB_SETUP_STATUS_PEND_OFFSET 5
#define CSR_USB_SETUP_STATUS_PEND_SIZE 1
#define CSR_USB_SETUP_STATUS_IS_IN_OFFSET 6
#define CSR_USB_SETUP_STATUS_IS_IN_SIZE 1
#define CSR_USB_SETUP_STATUS_DATA_OFFSET 7
#define CSR_USB_SETUP_STATUS_DATA_SIZE 1
#define CSR_USB_SETUP_EV_STATUS_ADDR 0xe0004818L
#define CSR_USB_SETUP_EV_STATUS_SIZE 1
static inline unsigned char usb_setup_ev_status_read(void) {
	unsigned char r = csr_readl(0xe0004818L);
	return r;
}
static inline void usb_setup_ev_status_write(unsigned char value) {
	csr_writel(value, 0xe0004818L);
}
#define CSR_USB_SETUP_EV_PENDING_ADDR 0xe000481cL
#define CSR_USB_SETUP_EV_PENDING_SIZE 1
static inline unsigned char usb_setup_ev_pending_read(void) {
	unsigned char r = csr_readl(0xe000481cL);
	return r;
}
static inline void usb_setup_ev_pending_write(unsigned char value) {
	csr_writel(value, 0xe000481cL);
}
#define CSR_USB_SETUP_EV_ENABLE_ADDR 0xe0004820L
#define CSR_USB_SETUP_EV_ENABLE_SIZE 1
static inline unsigned char usb_setup_ev_enable_read(void) {
	unsigned char r = csr_readl(0xe0004820L);
	return r;
}
static inline void usb_setup_ev_enable_write(unsigned char value) {
	csr_writel(value, 0xe0004820L);
}
#define CSR_USB_IN_DATA_ADDR 0xe0004824L
#define CSR_USB_IN_DATA_SIZE 1
static inline unsigned char usb_in_data_read(void) {
	unsigned char r = csr_readl(0xe0004824L);
	return r;
}
static inline void usb_in_data_write(unsigned char value) {
	csr_writel(value, 0xe0004824L);
}
#define CSR_USB_IN_DATA_DATA_OFFSET 0
#define CSR_USB_IN_DATA_DATA_SIZE 8
#define CSR_USB_IN_CTRL_ADDR 0xe0004828L
#define CSR_USB_IN_CTRL_SIZE 1
static inline unsigned char usb_in_ctrl_read(void) {
	unsigned char r = csr_readl(0xe0004828L);
	return r;
}
static inline void usb_in_ctrl_write(unsigned char value) {
	csr_writel(value, 0xe0004828L);
}
#define CSR_USB_IN_CTRL_EPNO_OFFSET 0
#define CSR_USB_IN_CTRL_EPNO_SIZE 4
#define CSR_USB_IN_CTRL_RESET_OFFSET 5
#define CSR_USB_IN_CTRL_RESET_SIZE 1
#define CSR_USB_IN_CTRL_STALL_OFFSET 6
#define CSR_USB_IN_CTRL_STALL_SIZE 1
#define CSR_USB_IN_STATUS_ADDR 0xe000482cL
#define CSR_USB_IN_STATUS_SIZE 1
static inline unsigned char usb_in_status_read(void) {
	unsigned char r = csr_readl(0xe000482cL);
	return r;
}
#define CSR_USB_IN_STATUS_IDLE_OFFSET 0
#define CSR_USB_IN_STATUS_IDLE_SIZE 1
#define CSR_USB_IN_STATUS_HAVE_OFFSET 4
#define CSR_USB_IN_STATUS_HAVE_SIZE 1
#define CSR_USB_IN_STATUS_PEND_OFFSET 5
#define CSR_USB_IN_STATUS_PEND_SIZE 1
#define CSR_USB_IN_EV_STATUS_ADDR 0xe0004830L
#define CSR_USB_IN_EV_STATUS_SIZE 1
static inline unsigned char usb_in_ev_status_read(void) {
	unsigned char r = csr_readl(0xe0004830L);
	return r;
}
static inline void usb_in_ev_status_write(unsigned char value) {
	csr_writel(value, 0xe0004830L);
}
#define CSR_USB_IN_EV_PENDING_ADDR 0xe0004834L
#define CSR_USB_IN_EV_PENDING_SIZE 1
static inline unsigned char usb_in_ev_pending_read(void) {
	unsigned char r = csr_readl(0xe0004834L);
	return r;
}
static inline void usb_in_ev_pending_write(unsigned char value) {
	csr_writel(value, 0xe0004834L);
}
#define CSR_USB_IN_EV_ENABLE_ADDR 0xe0004838L
#define CSR_USB_IN_EV_ENABLE_SIZE 1
static inline unsigned char usb_in_ev_enable_read(void) {
	unsigned char r = csr_readl(0xe0004838L);
	return r;
}
static inline void usb_in_ev_enable_write(unsigned char value) {
	csr_writel(value, 0xe0004838L);
}
#define CSR_USB_OUT_DATA_ADDR 0xe000483cL
#define CSR_USB_OUT_DATA_SIZE 1
static inline unsigned char usb_out_data_read(void) {
	unsigned char r = csr_readl(0xe000483cL);
	return r;
}
#define CSR_USB_OUT_DATA_DATA_OFFSET 0
#define CSR_USB_OUT_DATA_DATA_SIZE 8
#define CSR_USB_OUT_CTRL_ADDR 0xe0004840L
#define CSR_USB_OUT_CTRL_SIZE 1
static inline unsigned char usb_out_ctrl_read(void) {
	unsigned char r = csr_readl(0xe0004840L);
	return r;
}
static inline void usb_out_ctrl_write(unsigned char value) {
	csr_writel(value, 0xe0004840L);
}
#define CSR_USB_OUT_CTRL_EPNO_OFFSET 0
#define CSR_USB_OUT_CTRL_EPNO_SIZE 4
#define CSR_USB_OUT_CTRL_ENABLE_OFFSET 4
#define CSR_USB_OUT_CTRL_ENABLE_SIZE 1
#define CSR_USB_OUT_CTRL_RESET_OFFSET 5
#define CSR_USB_OUT_CTRL_RESET_SIZE 1
#define CSR_USB_OUT_CTRL_STALL_OFFSET 6
#define CSR_USB_OUT_CTRL_STALL_SIZE 1
#define CSR_USB_OUT_STATUS_ADDR 0xe0004844L
#define CSR_USB_OUT_STATUS_SIZE 1
static inline unsigned char usb_out_status_read(void) {
	unsigned char r = csr_readl(0xe0004844L);
	return r;
}
#define CSR_USB_OUT_STATUS_EPNO_OFFSET 0
#define CSR_USB_OUT_STATUS_EPNO_SIZE 4
#define CSR_USB_OUT_STATUS_HAVE_OFFSET 4
#define CSR_USB_OUT_STATUS_HAVE_SIZE 1
#define CSR_USB_OUT_STATUS_PEND_OFFSET 5
#define CSR_USB_OUT_STATUS_PEND_SIZE 1
#define CSR_USB_OUT_EV_STATUS_ADDR 0xe0004848L
#define CSR_USB_OUT_EV_STATUS_SIZE 1
static inline unsigned char usb_out_ev_status_read(void) {
	unsigned char r = csr_readl(0xe0004848L);
	return r;
}
static inline void usb_out_ev_status_write(unsigned char value) {
	csr_writel(value, 0xe0004848L);
}
#define CSR_USB_OUT_EV_PENDING_ADDR 0xe000484cL
#define CSR_USB_OUT_EV_PENDING_SIZE 1
static inline unsigned char usb_out_ev_pending_read(void) {
	unsigned char r = csr_readl(0xe000484cL);
	return r;
}
static inline void usb_out_ev_pending_write(unsigned char value) {
	csr_writel(value, 0xe000484cL);
}
#define CSR_USB_OUT_EV_ENABLE_ADDR 0xe0004850L
#define CSR_USB_OUT_EV_ENABLE_SIZE 1
static inline unsigned char usb_out_ev_enable_read(void) {
	unsigned char r = csr_readl(0xe0004850L);
	return r;
}
static inline void usb_out_ev_enable_write(unsigned char value) {
	csr_writel(value, 0xe0004850L);
}
#define CSR_USB_OUT_ENABLE_STATUS_ADDR 0xe0004854L
#define CSR_USB_OUT_ENABLE_STATUS_SIZE 1
static inline unsigned char usb_out_enable_status_read(void) {
	unsigned char r = csr_readl(0xe0004854L);
	return r;
}
#define CSR_USB_OUT_STALL_STATUS_ADDR 0xe0004858L
#define CSR_USB_OUT_STALL_STATUS_SIZE 1
static inline unsigned char usb_out_stall_status_read(void) {
	unsigned char r = csr_readl(0xe0004858L);
	return r;
}

/* version */
#define CSR_VERSION_BASE 0xe0007000L
#define CSR_VERSION_MAJOR_ADDR 0xe0007000L
#define CSR_VERSION_MAJOR_SIZE 1
static inline unsigned char version_major_read(void) {
	unsigned char r = csr_readl(0xe0007000L);
	return r;
}
#define CSR_VERSION_MINOR_ADDR 0xe0007004L
#define CSR_VERSION_MINOR_SIZE 1
static inline unsigned char version_minor_read(void) {
	unsigned char r = csr_readl(0xe0007004L);
	return r;
}
#define CSR_VERSION_REVISION_ADDR 0xe0007008L
#define CSR_VERSION_REVISION_SIZE 1
static inline unsigned char version_revision_read(void) {
	unsigned char r = csr_readl(0xe0007008L);
	return r;
}
#define CSR_VERSION_GITREV_ADDR 0xe000700cL
#define CSR_VERSION_GITREV_SIZE 4
static inline unsigned int version_gitrev_read(void) {
	unsigned int r = csr_readl(0xe000700cL);
	r <<= 8;
	r |= csr_readl(0xe0007010L);
	r <<= 8;
	r |= csr_readl(0xe0007014L);
	r <<= 8;
	r |= csr_readl(0xe0007018L);
	return r;
}
#define CSR_VERSION_GITEXTRA_ADDR 0xe000701cL
#define CSR_VERSION_GITEXTRA_SIZE 2
static inline unsigned short int version_gitextra_read(void) {
	unsigned short int r = csr_readl(0xe000701cL);
	r <<= 8;
	r |= csr_readl(0xe0007020L);
	return r;
}
#define CSR_VERSION_DIRTY_ADDR 0xe0007024L
#define CSR_VERSION_DIRTY_SIZE 1
static inline unsigned char version_dirty_read(void) {
	unsigned char r = csr_readl(0xe0007024L);
	return r;
}
#define CSR_VERSION_DIRTY_DIRTY_OFFSET 0
#define CSR_VERSION_DIRTY_DIRTY_SIZE 1
#define CSR_VERSION_MODEL_ADDR 0xe0007028L
#define CSR_VERSION_MODEL_SIZE 1
static inline unsigned char version_model_read(void) {
	unsigned char r = csr_readl(0xe0007028L);
	return r;
}
#define CSR_VERSION_MODEL_MODEL_OFFSET 0
#define CSR_VERSION_MODEL_MODEL_SIZE 8
#define CSR_VERSION_SEED_ADDR 0xe000702cL
#define CSR_VERSION_SEED_SIZE 4
static inline unsigned int version_seed_read(void) {
	unsigned int r = csr_readl(0xe000702cL);
	r <<= 8;
	r |= csr_readl(0xe0007030L);
	r <<= 8;
	r |= csr_readl(0xe0007034L);
	r <<= 8;
	r |= csr_readl(0xe0007038L);
	return r;
}

/* constants */
#define TIMER0_INTERRUPT 2
static inline int timer0_interrupt_read(void) {
	return 2;
}
#define USB_INTERRUPT 3
static inline int usb_interrupt_read(void) {
	return 3;
}
#define CONFIG_BITSTREAM_SYNC_HEADER1 2123999870
static inline int config_bitstream_sync_header1_read(void) {
	return 2123999870;
}
#define CONFIG_BITSTREAM_SYNC_HEADER2 2125109630
static inline int config_bitstream_sync_header2_read(void) {
	return 2125109630;
}
#define CONFIG_CLOCK_FREQUENCY 12000000
static inline int config_clock_frequency_read(void) {
	return 12000000;
}
#define CONFIG_CPU_RESET_ADDR 0
static inline int config_cpu_reset_addr_read(void) {
	return 0;
}
#define CONFIG_CPU_TYPE "VEXRISCV"
static inline const char * config_cpu_type_read(void) {
	return "VEXRISCV";
}
#define CONFIG_CPU_TYPE_VEXRISCV 1
static inline int config_cpu_type_vexriscv_read(void) {
	return 1;
}
#define CONFIG_CPU_VARIANT "MIN"
static inline const char * config_cpu_variant_read(void) {
	return "MIN";
}
#define CONFIG_CPU_VARIANT_MIN 1
static inline int config_cpu_variant_min_read(void) {
	return 1;
}
#define CONFIG_CSR_ALIGNMENT 32
static inline int config_csr_alignment_read(void) {
	return 32;
}
#define CONFIG_CSR_DATA_WIDTH 8
static inline int config_csr_data_width_read(void) {
	return 8;
}

#endif

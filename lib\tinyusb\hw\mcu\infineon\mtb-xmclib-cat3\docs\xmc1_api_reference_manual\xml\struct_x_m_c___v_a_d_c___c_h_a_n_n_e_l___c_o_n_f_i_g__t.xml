<?xml version='1.0' encoding='UTF-8' standalone='no'?>
<doxygen xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="compound.xsd" version="1.8.15">
  <compounddef id="struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t" kind="struct" language="C++" prot="public">
    <compoundname>XMC_VADC_CHANNEL_CONFIG_t</compoundname>
    <includes local="no">xmc_vadc.h</includes>
      <sectiondef kind="public-attrib">
      <memberdef kind="variable" id="struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t_1a0b8363af58af83145fe1da8d363457fb" prot="public" static="no" mutable="no">
        <type>union XMC_VADC_CHANNEL_CONFIG_t::@161</type>
        <definition>union XMC_VADC_CHANNEL_CONFIG_t::@161 @162</definition>
        <argsstring></argsstring>
        <name>@162</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" line="901" column="1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t_1acf62a24e38f80ac9b185f656af8390dd" prot="public" static="no" mutable="no">
        <type>union XMC_VADC_CHANNEL_CONFIG_t::@163</type>
        <definition>union XMC_VADC_CHANNEL_CONFIG_t::@163 @164</definition>
        <argsstring></argsstring>
        <name>@164</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" line="935" column="1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t_1a2a324325588ff4e4883bca2be519d94c" prot="public" static="no" mutable="no">
        <type>union XMC_VADC_CHANNEL_CONFIG_t::@165</type>
        <definition>union XMC_VADC_CHANNEL_CONFIG_t::@165 @166</definition>
        <argsstring></argsstring>
        <name>@166</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" line="952" column="1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t_1a3e57c2ef1c3ffb36722f000cc1156824" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t __pad0__</definition>
        <argsstring></argsstring>
        <name>__pad0__</name>
        <bitfield> 2</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" line="880" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" bodystart="880" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t_1a6712ba6dd1d5b43d2d56ff8ac4e275a7" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t __pad1__</definition>
        <argsstring></argsstring>
        <name>__pad1__</name>
        <bitfield> 4</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" line="890" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" bodystart="890" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t_1a9ce12a63de64ef64ae2d59d128251cae" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t __pad2__</definition>
        <argsstring></argsstring>
        <name>__pad2__</name>
        <bitfield> 6</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" line="895" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" bodystart="895" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t_1a8a131e0b7795d896bed92b0b774a64eb" prot="public" static="no" mutable="no">
        <type>int8_t</type>
        <definition>int8_t alias_channel</definition>
        <argsstring></argsstring>
        <name>alias_channel</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>Specifies the channel which has to be aliased with CH0/CH1 (which ever is applicable). Force the value to <bold></bold>(int8_t)-1 to bypass alias feature. Uses <ref refid="group___v_a_d_c_1ga32e761497c835cb7995991380b8b9676" kindref="member">XMC_VADC_CHANNEL_ALIAS_t</ref> for configuration. </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" line="955" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" bodystart="955" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t_1abc694c4a46e96fa73702976d69b0619f" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t alternate_reference</definition>
        <argsstring></argsstring>
        <name>alternate_reference</name>
        <bitfield> 1</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>Input reference voltage selection either VARef or CH-0. Uses <ref refid="group___v_a_d_c_1gab0c1eb7f8d8743b6b98ebfe8acd14e28" kindref="member">XMC_VADC_CHANNEL_REF_t</ref> </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" line="888" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" bodystart="888" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t_1a6bd36f0b7d0a4acb1defb5c7e9b574d2" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t bfl</definition>
        <argsstring></argsstring>
        <name>bfl</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" line="934" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" bodystart="934" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t_1aed9552b866e1ba693d36c0af702080f9" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t bflc</definition>
        <argsstring></argsstring>
        <name>bflc</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" line="951" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" bodystart="951" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t_1a7fb2414d385e31b4446533fd3e789b48" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t boundary_flag_mode_ch0</definition>
        <argsstring></argsstring>
        <name>boundary_flag_mode_ch0</name>
        <bitfield> 4</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>Specify the basic operation of boundary flag 0 Uses <ref refid="group___v_a_d_c_1ga017b3c6d3e14d46d5cf60602f262659d" kindref="member">XMC_VADC_GROUP_BOUNDARY_FLAG_MODE_t</ref> </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" line="941" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" bodystart="941" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t_1a1b16be24c58631bf71b7eac5d7eccfd6" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t boundary_flag_mode_ch1</definition>
        <argsstring></argsstring>
        <name>boundary_flag_mode_ch1</name>
        <bitfield> 4</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>Specify the basic operation of boundary flag 1 Uses <ref refid="group___v_a_d_c_1ga017b3c6d3e14d46d5cf60602f262659d" kindref="member">XMC_VADC_GROUP_BOUNDARY_FLAG_MODE_t</ref> </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" line="943" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" bodystart="943" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t_1a11e5c96f2e23416b48fcb0ea845dd231" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t boundary_flag_mode_ch2</definition>
        <argsstring></argsstring>
        <name>boundary_flag_mode_ch2</name>
        <bitfield> 4</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>Specify the basic operation of boundary flag 2 Uses <ref refid="group___v_a_d_c_1ga017b3c6d3e14d46d5cf60602f262659d" kindref="member">XMC_VADC_GROUP_BOUNDARY_FLAG_MODE_t</ref> </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" line="945" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" bodystart="945" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t_1aa9b6fa496ec6a057cebd4d74fff6a502" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t boundary_flag_mode_ch3</definition>
        <argsstring></argsstring>
        <name>boundary_flag_mode_ch3</name>
        <bitfield> 4</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>Specify the basic operation of boundary flag 3 Uses <ref refid="group___v_a_d_c_1ga017b3c6d3e14d46d5cf60602f262659d" kindref="member">XMC_VADC_GROUP_BOUNDARY_FLAG_MODE_t</ref> </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" line="947" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" bodystart="947" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t_1a3e8ebce22211f9d7c03e9fc7843fd8ea" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t broken_wire_detect</definition>
        <argsstring></argsstring>
        <name>broken_wire_detect</name>
        <bitfield> 1</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>Configures extra phase before the capacitor is sampled. </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" line="898" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" bodystart="898" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t_1a4d8031f9a26f99fde46015f8a65138b0" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t broken_wire_detect_channel</definition>
        <argsstring></argsstring>
        <name>broken_wire_detect_channel</name>
        <bitfield> 2</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>Source to be used to charge the capacitor for BWD feature. Uses <ref refid="group___v_a_d_c_1ga91049cf62e24988e2df8554af78e2183" kindref="member">XMC_VADC_CHANNEL_BWDCH_t</ref>. </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" line="896" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" bodystart="896" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t_1a5932b5c8ecb904ae3ed29ee37cedeeca" prot="public" static="no" mutable="no">
        <type>bool</type>
        <definition>bool channel_priority</definition>
        <argsstring></argsstring>
        <name>channel_priority</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>Only non priority channels can be converted by Background Request Source </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" line="954" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" bodystart="954" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t_1a02b0cafda03d57dc81206cf4295a6c17" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t chctr</definition>
        <argsstring></argsstring>
        <name>chctr</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" line="900" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" bodystart="900" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t_1ad6d48cf05e253058ff97116a5328d605" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t event_gen_criteria</definition>
        <argsstring></argsstring>
        <name>event_gen_criteria</name>
        <bitfield> 2</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>When should an event be generated? Uses <ref refid="group___v_a_d_c_1ga1b6bf4c15562eef67deac95cb36a48d6" kindref="member">XMC_VADC_CHANNEL_EVGEN_t</ref> </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" line="885" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" bodystart="885" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t_1ae37065e1c1d6bb46b3ac4cbe88dd43e8" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t flag_output_condition_ch0</definition>
        <argsstring></argsstring>
        <name>flag_output_condition_ch0</name>
        <bitfield> 1</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>Condition for which the boundary flag should change. Uses <ref refid="group___v_a_d_c_1ga69fe77fd05cfd4a502f14becf6352cea" kindref="member">XMC_VADC_CHANNEL_BOUNDARY_CONDITION_t</ref> </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" line="908" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" bodystart="908" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t_1a861117e4a2663d8c1c983881c6183a69" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t flag_output_condition_ch1</definition>
        <argsstring></argsstring>
        <name>flag_output_condition_ch1</name>
        <bitfield> 1</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>Condition for which the boundary flag should change. Uses <ref refid="group___v_a_d_c_1ga69fe77fd05cfd4a502f14becf6352cea" kindref="member">XMC_VADC_CHANNEL_BOUNDARY_CONDITION_t</ref> </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" line="910" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" bodystart="910" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t_1a1693c20a2895511e87e303e0b536b354" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t flag_output_condition_ch2</definition>
        <argsstring></argsstring>
        <name>flag_output_condition_ch2</name>
        <bitfield> 1</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>Condition for which the boundary flag should change. Uses <ref refid="group___v_a_d_c_1ga69fe77fd05cfd4a502f14becf6352cea" kindref="member">XMC_VADC_CHANNEL_BOUNDARY_CONDITION_t</ref> </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" line="912" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" bodystart="912" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t_1a1790b248fa78c94775340018d5d75eb9" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t flag_output_condition_ch3</definition>
        <argsstring></argsstring>
        <name>flag_output_condition_ch3</name>
        <bitfield> 1</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>Condition for which the boundary flag should change. Uses <ref refid="group___v_a_d_c_1ga69fe77fd05cfd4a502f14becf6352cea" kindref="member">XMC_VADC_CHANNEL_BOUNDARY_CONDITION_t</ref> </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" line="914" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" bodystart="914" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t_1a541d8cf63a55e89cd494c04ecbaba22d" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t input_class</definition>
        <argsstring></argsstring>
        <name>input_class</name>
        <bitfield> 2</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>Input conversion class selection. Uses <ref refid="group___v_a_d_c_1ga0da7a93877a25398b4e70482b85acd03" kindref="member">XMC_VADC_CHANNEL_CONV_t</ref> </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" line="878" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" bodystart="878" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t_1ae9bca220d6449a8cea505e236ffdbfaf" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t invert_boundary_flag_ch0</definition>
        <argsstring></argsstring>
        <name>invert_boundary_flag_ch0</name>
        <bitfield> 1</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>Inverts boundary flag output. </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" line="921" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" bodystart="921" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t_1a9f086dba469b4106f42ce3939a8f512f" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t invert_boundary_flag_ch1</definition>
        <argsstring></argsstring>
        <name>invert_boundary_flag_ch1</name>
        <bitfield> 1</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>Inverts boundary flag output. </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" line="922" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" bodystart="922" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t_1a5c43f21835d69a1cd550b844a648afa0" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t invert_boundary_flag_ch2</definition>
        <argsstring></argsstring>
        <name>invert_boundary_flag_ch2</name>
        <bitfield> 1</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>Inverts boundary flag output. </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" line="923" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" bodystart="923" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t_1a0b50807d5bdc349b0293d32200bf798e" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t invert_boundary_flag_ch3</definition>
        <argsstring></argsstring>
        <name>invert_boundary_flag_ch3</name>
        <bitfield> 1</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>Inverts boundary flag output. </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" line="924" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" bodystart="924" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t_1a176b7b24a27a13d8e4c9f3c793d8a33a" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t lower_boundary_select</definition>
        <argsstring></argsstring>
        <name>lower_boundary_select</name>
        <bitfield> 2</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>Which boundary register serves as lower bound? Accepts enum <ref refid="group___v_a_d_c_1ga203ae2271679fb3e05d185a6bd14b8b2" kindref="member">XMC_VADC_CHANNEL_BOUNDARY_t</ref> </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" line="881" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" bodystart="881" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t_1ab7905bc2512f8d908fcc1e0861216910" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t result_alignment</definition>
        <argsstring></argsstring>
        <name>result_alignment</name>
        <bitfield> 1</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>Alignment of the results read in the result register. Uses <ref refid="group___v_a_d_c_1ga5db9760a1339267ae158e21ee3047859" kindref="member">XMC_VADC_RESULT_ALIGN_t</ref> </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" line="893" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" bodystart="893" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t_1a6242812bf6bf8b9b28199831ef757c1f" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t result_reg_number</definition>
        <argsstring></argsstring>
        <name>result_reg_number</name>
        <bitfield> 4</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>Group result register number </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" line="891" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" bodystart="891" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t_1ac9b14d97abc821236d6aed27cf4c3cc0" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t sync_conversion</definition>
        <argsstring></argsstring>
        <name>sync_conversion</name>
        <bitfield> 1</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>Enables synchronous conversion for the configured channel </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" line="887" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" bodystart="887" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t_1a5e0a2b26dc7c9e1eee29fd690d2c6018" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t upper_boundary_select</definition>
        <argsstring></argsstring>
        <name>upper_boundary_select</name>
        <bitfield> 2</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>Which boundary register serves as upper bound? Accepts enum <ref refid="group___v_a_d_c_1ga203ae2271679fb3e05d185a6bd14b8b2" kindref="member">XMC_VADC_CHANNEL_BOUNDARY_t</ref> </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" line="883" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" bodystart="883" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t_1a8707bc8ffc791a9880f1b229d048516f" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t use_global_result</definition>
        <argsstring></argsstring>
        <name>use_global_result</name>
        <bitfield> 1</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>Use global result register for background request source channels </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" line="892" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" bodystart="892" bodyend="-1"/>
      </memberdef>
      </sectiondef>
    <briefdescription>
    </briefdescription>
    <detaileddescription>
<para>Structure initializing the VADC channel. Use type <ref refid="struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t" kindref="compound">XMC_VADC_CHANNEL_CONFIG_t</ref> for this enumeration. </para>
    </detaileddescription>
    <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" line="873" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_vadc.h" bodystart="872" bodyend="958"/>
    <listofallmembers>
      <member refid="struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t_1a3e57c2ef1c3ffb36722f000cc1156824" prot="public" virt="non-virtual"><scope>XMC_VADC_CHANNEL_CONFIG_t</scope><name>__pad0__</name></member>
      <member refid="struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t_1a6712ba6dd1d5b43d2d56ff8ac4e275a7" prot="public" virt="non-virtual"><scope>XMC_VADC_CHANNEL_CONFIG_t</scope><name>__pad1__</name></member>
      <member refid="struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t_1a9ce12a63de64ef64ae2d59d128251cae" prot="public" virt="non-virtual"><scope>XMC_VADC_CHANNEL_CONFIG_t</scope><name>__pad2__</name></member>
      <member refid="struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t_1a8a131e0b7795d896bed92b0b774a64eb" prot="public" virt="non-virtual"><scope>XMC_VADC_CHANNEL_CONFIG_t</scope><name>alias_channel</name></member>
      <member refid="struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t_1abc694c4a46e96fa73702976d69b0619f" prot="public" virt="non-virtual"><scope>XMC_VADC_CHANNEL_CONFIG_t</scope><name>alternate_reference</name></member>
      <member refid="struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t_1a6bd36f0b7d0a4acb1defb5c7e9b574d2" prot="public" virt="non-virtual"><scope>XMC_VADC_CHANNEL_CONFIG_t</scope><name>bfl</name></member>
      <member refid="struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t_1aed9552b866e1ba693d36c0af702080f9" prot="public" virt="non-virtual"><scope>XMC_VADC_CHANNEL_CONFIG_t</scope><name>bflc</name></member>
      <member refid="struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t_1a7fb2414d385e31b4446533fd3e789b48" prot="public" virt="non-virtual"><scope>XMC_VADC_CHANNEL_CONFIG_t</scope><name>boundary_flag_mode_ch0</name></member>
      <member refid="struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t_1a1b16be24c58631bf71b7eac5d7eccfd6" prot="public" virt="non-virtual"><scope>XMC_VADC_CHANNEL_CONFIG_t</scope><name>boundary_flag_mode_ch1</name></member>
      <member refid="struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t_1a11e5c96f2e23416b48fcb0ea845dd231" prot="public" virt="non-virtual"><scope>XMC_VADC_CHANNEL_CONFIG_t</scope><name>boundary_flag_mode_ch2</name></member>
      <member refid="struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t_1aa9b6fa496ec6a057cebd4d74fff6a502" prot="public" virt="non-virtual"><scope>XMC_VADC_CHANNEL_CONFIG_t</scope><name>boundary_flag_mode_ch3</name></member>
      <member refid="struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t_1a3e8ebce22211f9d7c03e9fc7843fd8ea" prot="public" virt="non-virtual"><scope>XMC_VADC_CHANNEL_CONFIG_t</scope><name>broken_wire_detect</name></member>
      <member refid="struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t_1a4d8031f9a26f99fde46015f8a65138b0" prot="public" virt="non-virtual"><scope>XMC_VADC_CHANNEL_CONFIG_t</scope><name>broken_wire_detect_channel</name></member>
      <member refid="struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t_1a5932b5c8ecb904ae3ed29ee37cedeeca" prot="public" virt="non-virtual"><scope>XMC_VADC_CHANNEL_CONFIG_t</scope><name>channel_priority</name></member>
      <member refid="struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t_1a02b0cafda03d57dc81206cf4295a6c17" prot="public" virt="non-virtual"><scope>XMC_VADC_CHANNEL_CONFIG_t</scope><name>chctr</name></member>
      <member refid="struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t_1ad6d48cf05e253058ff97116a5328d605" prot="public" virt="non-virtual"><scope>XMC_VADC_CHANNEL_CONFIG_t</scope><name>event_gen_criteria</name></member>
      <member refid="struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t_1ae37065e1c1d6bb46b3ac4cbe88dd43e8" prot="public" virt="non-virtual"><scope>XMC_VADC_CHANNEL_CONFIG_t</scope><name>flag_output_condition_ch0</name></member>
      <member refid="struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t_1a861117e4a2663d8c1c983881c6183a69" prot="public" virt="non-virtual"><scope>XMC_VADC_CHANNEL_CONFIG_t</scope><name>flag_output_condition_ch1</name></member>
      <member refid="struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t_1a1693c20a2895511e87e303e0b536b354" prot="public" virt="non-virtual"><scope>XMC_VADC_CHANNEL_CONFIG_t</scope><name>flag_output_condition_ch2</name></member>
      <member refid="struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t_1a1790b248fa78c94775340018d5d75eb9" prot="public" virt="non-virtual"><scope>XMC_VADC_CHANNEL_CONFIG_t</scope><name>flag_output_condition_ch3</name></member>
      <member refid="struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t_1a541d8cf63a55e89cd494c04ecbaba22d" prot="public" virt="non-virtual"><scope>XMC_VADC_CHANNEL_CONFIG_t</scope><name>input_class</name></member>
      <member refid="struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t_1ae9bca220d6449a8cea505e236ffdbfaf" prot="public" virt="non-virtual"><scope>XMC_VADC_CHANNEL_CONFIG_t</scope><name>invert_boundary_flag_ch0</name></member>
      <member refid="struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t_1a9f086dba469b4106f42ce3939a8f512f" prot="public" virt="non-virtual"><scope>XMC_VADC_CHANNEL_CONFIG_t</scope><name>invert_boundary_flag_ch1</name></member>
      <member refid="struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t_1a5c43f21835d69a1cd550b844a648afa0" prot="public" virt="non-virtual"><scope>XMC_VADC_CHANNEL_CONFIG_t</scope><name>invert_boundary_flag_ch2</name></member>
      <member refid="struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t_1a0b50807d5bdc349b0293d32200bf798e" prot="public" virt="non-virtual"><scope>XMC_VADC_CHANNEL_CONFIG_t</scope><name>invert_boundary_flag_ch3</name></member>
      <member refid="struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t_1a176b7b24a27a13d8e4c9f3c793d8a33a" prot="public" virt="non-virtual"><scope>XMC_VADC_CHANNEL_CONFIG_t</scope><name>lower_boundary_select</name></member>
      <member refid="struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t_1ab7905bc2512f8d908fcc1e0861216910" prot="public" virt="non-virtual"><scope>XMC_VADC_CHANNEL_CONFIG_t</scope><name>result_alignment</name></member>
      <member refid="struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t_1a6242812bf6bf8b9b28199831ef757c1f" prot="public" virt="non-virtual"><scope>XMC_VADC_CHANNEL_CONFIG_t</scope><name>result_reg_number</name></member>
      <member refid="struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t_1ac9b14d97abc821236d6aed27cf4c3cc0" prot="public" virt="non-virtual"><scope>XMC_VADC_CHANNEL_CONFIG_t</scope><name>sync_conversion</name></member>
      <member refid="struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t_1a5e0a2b26dc7c9e1eee29fd690d2c6018" prot="public" virt="non-virtual"><scope>XMC_VADC_CHANNEL_CONFIG_t</scope><name>upper_boundary_select</name></member>
      <member refid="struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t_1a8707bc8ffc791a9880f1b229d048516f" prot="public" virt="non-virtual"><scope>XMC_VADC_CHANNEL_CONFIG_t</scope><name>use_global_result</name></member>
    </listofallmembers>
  </compounddef>
</doxygen>

var searchData=
[
  ['latch_5finput0',['latch_input0',['../struct_x_m_c___e_c_a_t___p_o_r_t___c_t_r_l__t.html#a31dedc2c7ce3f3682a9c610fe0802368',1,'XMC_ECAT_PORT_CTRL_t']]],
  ['latch_5finput1',['latch_input1',['../struct_x_m_c___e_c_a_t___p_o_r_t___c_t_r_l__t.html#ac039c6ce864b47c14511ef580823fe88',1,'XMC_ECAT_PORT_CTRL_t']]],
  ['ldcmp',['LDCMP',['../struct_x_m_c___l_e_d_t_s___g_l_o_b_a_l__t.html#a0df70c149c31bf194ff1fd88c521a363',1,'XMC_LEDTS_GLOBAL_t']]],
  ['led_20and_20touch_2dsense_20control_28ledts_29',['LED and Touch-Sense control(LEDTS)',['../group___l_e_d_t_s.html',1,'']]],
  ['length',['length',['../struct_x_m_c___e_t_h___m_a_c___d_m_a___d_e_s_c__t.html#aa22e7675f77b1f89a273f53b2707fc8b',1,'XMC_ETH_MAC_DMA_DESC_t']]],
  ['level',['level',['../struct_x_m_c___c_c_u4___s_l_i_c_e___e_v_e_n_t___c_o_n_f_i_g__t.html#ab2d48470948228f6e17d1f73fa3ff8f6',1,'XMC_CCU4_SLICE_EVENT_CONFIG_t::level()'],['../struct_x_m_c___c_c_u8___s_l_i_c_e___e_v_e_n_t___c_o_n_f_i_g__t.html#a027e27e584a06a9d915e93819e75a3c0',1,'XMC_CCU8_SLICE_EVENT_CONFIG_t::level()'],['../struct_x_m_c___h_r_p_w_m___c_s_g___i_n_p_u_t___c_o_n_f_i_g__t.html#acd3fed7f3d2dad51b21f5f3ae830e0ef',1,'XMC_HRPWM_CSG_INPUT_CONFIG_t::level()']]],
  ['line',['LINE',['../struct_x_m_c___l_e_d_t_s___g_l_o_b_a_l__t.html#ae0b99fb1c981fb35088cd22a2d5b2005',1,'XMC_LEDTS_GLOBAL_t']]],
  ['link',['link',['../struct_x_m_c___e_c_a_t___p_o_r_t___c_t_r_l__t.html#a6163461798acb053ba062347095330e0',1,'XMC_ECAT_PORT_CTRL_t']]],
  ['linked_5flist_5fpointer',['linked_list_pointer',['../struct_x_m_c___d_m_a___c_h___c_o_n_f_i_g__t.html#afe9ce0e84b4b1af842f404ab934b30ae',1,'XMC_DMA_CH_CONFIG_t']]],
  ['llp',['llp',['../struct_x_m_c___d_m_a___l_l_i__t.html#ab2814fa924e173e03b62c0b1079db01f',1,'XMC_DMA_LLI_t']]],
  ['load_5fmode',['load_mode',['../struct_x_m_c___v_a_d_c___s_c_a_n___c_o_n_f_i_g__t.html#aba4d2980c02b6afe5eae587ae27b3835',1,'XMC_VADC_SCAN_CONFIG_t']]],
  ['lower_5fboundary',['lower_boundary',['../struct_x_m_c___d_s_d___c_h___a_u_x___f_i_l_t_e_r___c_o_n_f_i_g__t.html#a741fbbef53687cf0c063593eb8b0ff55',1,'XMC_DSD_CH_AUX_FILTER_CONFIG_t']]],
  ['lower_5fboundary_5fselect',['lower_boundary_select',['../struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t.html#a176b7b24a27a13d8e4c9f3c793d8a33a',1,'XMC_VADC_CHANNEL_CONFIG_t']]]
];

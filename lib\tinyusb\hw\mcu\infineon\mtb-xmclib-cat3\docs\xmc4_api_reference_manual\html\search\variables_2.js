var searchData=
[
  ['baudrate',['baudrate',['../struct_x_m_c___c_a_n___n_o_d_e___n_o_m_i_n_a_l___b_i_t___t_i_m_e___c_o_n_f_i_g__t.html#ac4f06ea26ed6bd7ae83b92d64ac10b78',1,'XMC_CAN_NODE_NOMINAL_BIT_TIME_CONFIG_t::baudrate()'],['../struct_x_m_c___i2_c___c_h___c_o_n_f_i_g__t.html#ac4f06ea26ed6bd7ae83b92d64ac10b78',1,'XMC_I2C_CH_CONFIG_t::baudrate()'],['../struct_x_m_c___i2_s___c_h___c_o_n_f_i_g__t.html#ac4f06ea26ed6bd7ae83b92d64ac10b78',1,'XMC_I2S_CH_CONFIG_t::baudrate()'],['../struct_x_m_c___s_p_i___c_h___c_o_n_f_i_g__t.html#ac4f06ea26ed6bd7ae83b92d64ac10b78',1,'XMC_SPI_CH_CONFIG_t::baudrate()'],['../struct_x_m_c___u_a_r_t___c_h___c_o_n_f_i_g__t.html#ac4f06ea26ed6bd7ae83b92d64ac10b78',1,'XMC_UART_CH_CONFIG_t::baudrate()']]],
  ['bit_5freverse',['bit_reverse',['../struct_x_m_c___d_s_d___g_e_n_e_r_a_t_o_r___c_o_n_f_i_g__t.html#a35e60d84658d7ee5c07092fdf99e2054',1,'XMC_DSD_GENERATOR_CONFIG_t']]],
  ['blank_5fext_5fenable',['blank_ext_enable',['../struct_x_m_c___h_r_p_w_m___c_s_g___c_m_p__t.html#a4f7f935f7bc5b46198411d97b68c633f',1,'XMC_HRPWM_CSG_CMP_t']]],
  ['blanking_5fmode',['blanking_mode',['../struct_x_m_c___h_r_p_w_m___c_s_g___c_m_p__t.html#afef925764e58672f6cc37c6af986a785',1,'XMC_HRPWM_CSG_CMP_t']]],
  ['blanking_5fval',['blanking_val',['../struct_x_m_c___h_r_p_w_m___c_s_g___c_m_p__t.html#ad5958626a08bf7f69428d46560047454',1,'XMC_HRPWM_CSG_CMP_t']]],
  ['block_5fsize',['block_size',['../struct_x_m_c___d_m_a___l_l_i__t.html#a9e3fb1e50a1c71b2337df296222d9553',1,'XMC_DMA_LLI_t::block_size()'],['../struct_x_m_c___d_m_a___c_h___c_o_n_f_i_g__t.html#a9d83aa5f365f4a1b62dd70bd1ae426a3',1,'XMC_DMA_CH_CONFIG_t::block_size()']]],
  ['bmi',['bmi',['../struct_x_m_c___f_l_a_s_h___b_m_i___s_t_r_i_n_g__t.html#a6ae51eccb75069dae7f1ebab13955aac',1,'XMC_FLASH_BMI_STRING_t']]],
  ['boundary0',['boundary0',['../struct_x_m_c___v_a_d_c___g_l_o_b_a_l___c_o_n_f_i_g__t.html#afa1f8d0e8c195a4e11c173e796a0ccfa',1,'XMC_VADC_GLOBAL_CONFIG_t::boundary0()'],['../struct_x_m_c___v_a_d_c___g_r_o_u_p___c_o_n_f_i_g__t.html#afa1f8d0e8c195a4e11c173e796a0ccfa',1,'XMC_VADC_GROUP_CONFIG_t::boundary0()']]],
  ['boundary1',['boundary1',['../struct_x_m_c___v_a_d_c___g_l_o_b_a_l___c_o_n_f_i_g__t.html#a526defd6dde917b2a3eb34bb6da90f83',1,'XMC_VADC_GLOBAL_CONFIG_t::boundary1()'],['../struct_x_m_c___v_a_d_c___g_r_o_u_p___c_o_n_f_i_g__t.html#a526defd6dde917b2a3eb34bb6da90f83',1,'XMC_VADC_GROUP_CONFIG_t::boundary1()']]],
  ['boundary_5fflag_5fmode_5fch0',['boundary_flag_mode_ch0',['../struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t.html#a7fb2414d385e31b4446533fd3e789b48',1,'XMC_VADC_CHANNEL_CONFIG_t']]],
  ['boundary_5fflag_5fmode_5fch1',['boundary_flag_mode_ch1',['../struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t.html#a1b16be24c58631bf71b7eac5d7eccfd6',1,'XMC_VADC_CHANNEL_CONFIG_t']]],
  ['boundary_5fflag_5fmode_5fch2',['boundary_flag_mode_ch2',['../struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t.html#a11e5c96f2e23416b48fcb0ea845dd231',1,'XMC_VADC_CHANNEL_CONFIG_t']]],
  ['boundary_5fflag_5fmode_5fch3',['boundary_flag_mode_ch3',['../struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t.html#aa9b6fa496ec6a057cebd4d74fff6a502',1,'XMC_VADC_CHANNEL_CONFIG_t']]],
  ['brg',['BRG',['../struct_x_m_c___u_s_i_c___c_h__t.html#a60442d8bed2e194af31a7bedbabf0c16',1,'XMC_USIC_CH_t']]],
  ['broken_5fwire_5fdetect',['broken_wire_detect',['../struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t.html#a3e8ebce22211f9d7c03e9fc7843fd8ea',1,'XMC_VADC_CHANNEL_CONFIG_t']]],
  ['broken_5fwire_5fdetect_5fchannel',['broken_wire_detect_channel',['../struct_x_m_c___v_a_d_c___c_h_a_n_n_e_l___c_o_n_f_i_g__t.html#a4d8031f9a26f99fde46015f8a65138b0',1,'XMC_VADC_CHANNEL_CONFIG_t']]],
  ['buffer1',['buffer1',['../struct_x_m_c___e_t_h___m_a_c___d_m_a___d_e_s_c__t.html#a457828e16cd02e6193af5a4ead1f3724',1,'XMC_ETH_MAC_DMA_DESC_t']]],
  ['buffer2',['buffer2',['../struct_x_m_c___e_t_h___m_a_c___d_m_a___d_e_s_c__t.html#a52d10c47bbc622bdaf7bfdf8793b2ea6',1,'XMC_ETH_MAC_DMA_DESC_t']]],
  ['buffer_5fread_5fenable',['buffer_read_enable',['../union_x_m_c___s_d_m_m_c___p_r_e_s_e_n_t___s_t_a_t_e__t.html#a06adf01a5f9d38bee11f694421ef5f55',1,'XMC_SDMMC_PRESENT_STATE_t']]],
  ['buffer_5fwrite_5fenable',['buffer_write_enable',['../union_x_m_c___s_d_m_m_c___p_r_e_s_e_n_t___s_t_a_t_e__t.html#ab25ce9bbe6110b28f70ad1fc88b22325',1,'XMC_SDMMC_PRESENT_STATE_t']]],
  ['bus_5fmode',['bus_mode',['../struct_x_m_c___i2_s___c_h___c_o_n_f_i_g__t.html#a2c2990cf73c767157446d6dbf5080994',1,'XMC_I2S_CH_CONFIG_t::bus_mode()'],['../struct_x_m_c___s_p_i___c_h___c_o_n_f_i_g__t.html#a2de71bb03e089719e03814340d4cf51d',1,'XMC_SPI_CH_CONFIG_t::bus_mode()']]],
  ['bus_5ftimeout_5fcontrol',['bus_timeout_control',['../struct_x_m_c___e_b_u___m_o_d_e___c_o_n_f_i_g__t.html#ae7332b8d91580f896a87312900287eaa',1,'XMC_EBU_MODE_CONFIG_t']]],
  ['bus_5fwidth',['bus_width',['../struct_x_m_c___s_d_m_m_c___c_o_n_f_i_g__t.html#aeee0d227b46878e64ce8a05f584a53f2',1,'XMC_SDMMC_CONFIG_t']]],
  ['byp',['BYP',['../struct_x_m_c___u_s_i_c___c_h__t.html#a5c5375cd6029e464a9f3e5400662ea73',1,'XMC_USIC_CH_t']]],
  ['bypcr',['BYPCR',['../struct_x_m_c___u_s_i_c___c_h__t.html#ab5bada982273b48262b30b5b52b9911a',1,'XMC_USIC_CH_t']]]
];

<!DOCTYPE Linker_Placement_File>
<Root name="Flash Section Placement">
  <MemorySegment name="$(FLASH_NAME:FLASH)">
    <ProgramSection alignment="0x100" load="Yes" name=".vectors" start="$(FLASH_START:)" />
    <ProgramSection alignment="4" load="Yes" name=".init" />
    <ProgramSection alignment="4" load="Yes" name=".init_rodata" />
    <ProgramSection alignment="4" load="Yes" name=".text" />
    <ProgramSection alignment="4" load="Yes" name=".dtors" />
    <ProgramSection alignment="4" load="Yes" name=".ctors" />
    <ProgramSection alignment="4" load="Yes" name=".rodata" />
    <ProgramSection alignment="4" load="Yes" name=".ARM.exidx" address_symbol="__exidx_start" end_symbol="__exidx_end" />
    <ProgramSection alignment="4" load="Yes" runin=".fast_run" name=".fast" />
    <ProgramSection alignment="4" load="Yes" runin=".data_run" name=".data" />
    <ProgramSection alignment="4" load="Yes" runin=".tdata_run" name=".tdata" />
  </MemorySegment>
  <MemorySegment name="$(RAM_NAME:RAM);SRAM">
    <ProgramSection alignment="0x100" load="No" name=".vectors_ram" start="$(RAM_START:$(SRAM_START:))" />
    <ProgramSection alignment="4" load="No" name=".fast_run" />
    <ProgramSection alignment="4" load="No" name=".data_run" />
    <ProgramSection alignment="4" load="No" name=".tdata_run" />
    <ProgramSection alignment="4" load="No" name=".bss" />
    <ProgramSection alignment="4" load="No" name=".tbss" />
    <ProgramSection alignment="4" load="No" name=".non_init" />
    <ProgramSection alignment="4" size="__HEAPSIZE__" load="No" name=".heap" />
    <ProgramSection alignment="8" size="__STACKSIZE__" load="No" place_from_segment_end="Yes" name=".stack" />
    <ProgramSection alignment="8" size="__STACKSIZE_PROCESS__" load="No" name=".stack_process" />
  </MemorySegment>
  <MemorySegment name="$(FLASH2_NAME:BOOTLOADER_SETTINGS)">
    <ProgramSection alignment="4" load="No" name=".bootloaderSettings" />
  </MemorySegment>
  <MemorySegment name="$(FLASH3_NAME:MBR_PARAMS_PAGE)">
    <ProgramSection alignment="4" load="No" name=".mbrParamsPage" keep="Yes" />
  </MemorySegment>
  <MemorySegment name="$(FLASH4_NAME:UICR_BOOTLOADER)">
    <ProgramSection alignment="4" load="Yes" name=".uicrBootStartAddress" keep="Yes" />
  </MemorySegment>
  <MemorySegment name="$(FLASH5_NAME:UICR_MBR_PARAM_PAGE)">
    <ProgramSection alignment="4" load="Yes" name=".uicrMbrParamsPageAddress" keep="Yes" />
  </MemorySegment>
  <MemorySegment name="$(RAM2_NAME:NOINIT)">
    <ProgramSection alignment="4" load="No" name=".noinit" />
  </MemorySegment>
</Root>

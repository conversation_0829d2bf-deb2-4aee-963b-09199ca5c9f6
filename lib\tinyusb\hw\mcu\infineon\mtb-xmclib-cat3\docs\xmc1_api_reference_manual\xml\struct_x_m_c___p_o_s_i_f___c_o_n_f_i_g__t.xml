<?xml version='1.0' encoding='UTF-8' standalone='no'?>
<doxygen xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="compound.xsd" version="1.8.15">
  <compounddef id="struct_x_m_c___p_o_s_i_f___c_o_n_f_i_g__t" kind="struct" language="C++" prot="public">
    <compoundname>XMC_POSIF_CONFIG_t</compoundname>
    <includes local="no">xmc_posif.h</includes>
      <sectiondef kind="public-attrib">
      <memberdef kind="variable" id="struct_x_m_c___p_o_s_i_f___c_o_n_f_i_g__t_1a159b9b301b709b07eaac9a7aba3d1d18" prot="public" static="no" mutable="no">
        <type>union XMC_POSIF_CONFIG_t::@129</type>
        <definition>union XMC_POSIF_CONFIG_t::@129 @130</definition>
        <argsstring></argsstring>
        <name>@130</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_posif.h" line="380" column="1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___p_o_s_i_f___c_o_n_f_i_g__t_1a3e57c2ef1c3ffb36722f000cc1156824" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t __pad0__</definition>
        <argsstring></argsstring>
        <name>__pad0__</name>
        <bitfield> 6</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_posif.h" line="371" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_posif.h" bodystart="371" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___p_o_s_i_f___c_o_n_f_i_g__t_1a6712ba6dd1d5b43d2d56ff8ac4e275a7" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t __pad1__</definition>
        <argsstring></argsstring>
        <name>__pad1__</name>
        <bitfield> 14</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_posif.h" line="375" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_posif.h" bodystart="375" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___p_o_s_i_f___c_o_n_f_i_g__t_1a9ce12a63de64ef64ae2d59d128251cae" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t __pad2__</definition>
        <argsstring></argsstring>
        <name>__pad2__</name>
        <bitfield> 1</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_posif.h" line="377" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_posif.h" bodystart="377" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___p_o_s_i_f___c_o_n_f_i_g__t_1a2719b8cbe8dc1a5064a418269c72e528" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t filter</definition>
        <argsstring></argsstring>
        <name>filter</name>
        <bitfield> 3</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>Input filter configuration </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_posif.h" line="376" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_posif.h" bodystart="376" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___p_o_s_i_f___c_o_n_f_i_g__t_1ad42172221b4e4252ffe294e08c433384" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t input0</definition>
        <argsstring></argsstring>
        <name>input0</name>
        <bitfield> 2</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>Choice of input for Input-1 </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_posif.h" line="372" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_posif.h" bodystart="372" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___p_o_s_i_f___c_o_n_f_i_g__t_1a64ceff8d404649c81159566611a1d871" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t input1</definition>
        <argsstring></argsstring>
        <name>input1</name>
        <bitfield> 2</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>Choice of input for Input-2 </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_posif.h" line="373" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_posif.h" bodystart="373" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___p_o_s_i_f___c_o_n_f_i_g__t_1ab457196ad0bef14fef35d05a51f80b32" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t input2</definition>
        <argsstring></argsstring>
        <name>input2</name>
        <bitfield> 2</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>Choice of input for Input-3 </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_posif.h" line="374" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_posif.h" bodystart="374" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___p_o_s_i_f___c_o_n_f_i_g__t_1a6b29e4f37f4482274af785ad5ffe96a7" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t mode</definition>
        <argsstring></argsstring>
        <name>mode</name>
        <bitfield> 2</bitfield>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
<para>POSIF Operational mode. Use <ref refid="group___p_o_s_i_f_1ga6599bce3683031aac9f154c47776b4f9" kindref="member">XMC_POSIF_MODE_t</ref> to configure </para>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_posif.h" line="370" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_posif.h" bodystart="370" bodyend="-1"/>
      </memberdef>
      <memberdef kind="variable" id="struct_x_m_c___p_o_s_i_f___c_o_n_f_i_g__t_1a9a8cf89b7913f0ce03d13f64d8e3f673" prot="public" static="no" mutable="no">
        <type>uint32_t</type>
        <definition>uint32_t pconf</definition>
        <argsstring></argsstring>
        <name>pconf</name>
        <briefdescription>
        </briefdescription>
        <detaileddescription>
        </detaileddescription>
        <inbodydescription>
        </inbodydescription>
        <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_posif.h" line="379" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_posif.h" bodystart="379" bodyend="-1"/>
      </memberdef>
      </sectiondef>
    <briefdescription>
    </briefdescription>
    <detaileddescription>
<para>Defines POSIF module initialization data structure. Use type <ref refid="struct_x_m_c___p_o_s_i_f___c_o_n_f_i_g__t" kindref="compound">XMC_POSIF_CONFIG_t</ref> for this data structure. It is used to initialize POSIF module using <emphasis>PCONF</emphasis> register. </para>
    </detaileddescription>
    <location file="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_posif.h" line="365" column="1" bodyfile="/var/tmp/gitlab-runner1/builds/xyH-6YS9/0/repo/mtb-xmclib-cat3/XMCLib/inc/xmc_posif.h" bodystart="364" bodyend="381"/>
    <listofallmembers>
      <member refid="struct_x_m_c___p_o_s_i_f___c_o_n_f_i_g__t_1a3e57c2ef1c3ffb36722f000cc1156824" prot="public" virt="non-virtual"><scope>XMC_POSIF_CONFIG_t</scope><name>__pad0__</name></member>
      <member refid="struct_x_m_c___p_o_s_i_f___c_o_n_f_i_g__t_1a6712ba6dd1d5b43d2d56ff8ac4e275a7" prot="public" virt="non-virtual"><scope>XMC_POSIF_CONFIG_t</scope><name>__pad1__</name></member>
      <member refid="struct_x_m_c___p_o_s_i_f___c_o_n_f_i_g__t_1a9ce12a63de64ef64ae2d59d128251cae" prot="public" virt="non-virtual"><scope>XMC_POSIF_CONFIG_t</scope><name>__pad2__</name></member>
      <member refid="struct_x_m_c___p_o_s_i_f___c_o_n_f_i_g__t_1a2719b8cbe8dc1a5064a418269c72e528" prot="public" virt="non-virtual"><scope>XMC_POSIF_CONFIG_t</scope><name>filter</name></member>
      <member refid="struct_x_m_c___p_o_s_i_f___c_o_n_f_i_g__t_1ad42172221b4e4252ffe294e08c433384" prot="public" virt="non-virtual"><scope>XMC_POSIF_CONFIG_t</scope><name>input0</name></member>
      <member refid="struct_x_m_c___p_o_s_i_f___c_o_n_f_i_g__t_1a64ceff8d404649c81159566611a1d871" prot="public" virt="non-virtual"><scope>XMC_POSIF_CONFIG_t</scope><name>input1</name></member>
      <member refid="struct_x_m_c___p_o_s_i_f___c_o_n_f_i_g__t_1ab457196ad0bef14fef35d05a51f80b32" prot="public" virt="non-virtual"><scope>XMC_POSIF_CONFIG_t</scope><name>input2</name></member>
      <member refid="struct_x_m_c___p_o_s_i_f___c_o_n_f_i_g__t_1a6b29e4f37f4482274af785ad5ffe96a7" prot="public" virt="non-virtual"><scope>XMC_POSIF_CONFIG_t</scope><name>mode</name></member>
      <member refid="struct_x_m_c___p_o_s_i_f___c_o_n_f_i_g__t_1a9a8cf89b7913f0ce03d13f64d8e3f673" prot="public" virt="non-virtual"><scope>XMC_POSIF_CONFIG_t</scope><name>pconf</name></member>
    </listofallmembers>
  </compounddef>
</doxygen>

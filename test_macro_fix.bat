@echo off
REM 测试宏修正 - 验证__VA_ARGS__问题是否已解决

echo ========================================
echo 测试可变参数宏修正
echo ========================================

REM 检查是否提供了板子名称参数
if "%1"=="" (
    echo 使用默认板子: feather_nrf52840_express
    set BOARD_NAME=feather_nrf52840_express
) else (
    set BOARD_NAME=%1
)

echo 目标板子: %BOARD_NAME%
echo.

echo [1/3] 清理之前的编译...
make BOARD=%BOARD_NAME% clean > nul 2>&1

echo [2/3] 测试编译boards.h相关文件...
echo 正在编译boards.c...

REM 尝试编译boards.c来测试宏定义
make BOARD=%BOARD_NAME% DEBUG=1 _build/build-%BOARD_NAME%/src/boards/boards.o
if errorlevel 1 (
    echo.
    echo ❌ boards.c 编译失败! 宏定义问题仍然存在
    echo.
    echo 可能的问题:
    echo 1. PRINTF宏定义不正确
    echo 2. __VA_ARGS__ 语法错误
    echo 3. 其他宏定义问题
    goto :end
) else (
    echo ✅ boards.c 编译成功!
)

echo.
echo [3/3] 测试编译main.c...
echo 正在编译main.c...

REM 尝试编译main.c来测试完整的头文件包含
make BOARD=%BOARD_NAME% DEBUG=1 _build/build-%BOARD_NAME%/src/main.o
if errorlevel 1 (
    echo.
    echo ❌ main.c 编译失败! 可能还有其他问题
    echo.
    echo 请检查编译错误信息
    goto :end
) else (
    echo ✅ main.c 编译成功!
)

echo.
echo ========================================
echo ✅ 所有测试通过! 宏定义问题已解决
echo ========================================
echo.
echo 修正内容:
echo   PRINTF(...)           SEGGER_RTT_printf(0, __VA_ARGS__)
echo   ^^^^^^^^^^ 添加了可变参数 (...)
echo.
echo 现在可以进行完整编译:
echo   make BOARD=%BOARD_NAME% DEBUG=1 all
echo.
echo 或者直接烧录:
echo   make BOARD=%BOARD_NAME% DEBUG=1 all
echo   make BOARD=%BOARD_NAME% flash

:end
pause

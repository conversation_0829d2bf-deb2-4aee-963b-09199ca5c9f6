{% macro peripheral(name, base_address) -%}
    <peripheral>
      <name>{{ name }}</name>
      <description>Broadcom Serial Controller (I2C compatible)</description>
      <baseAddress>{{ "0x{:x}".format(base_address) }}</baseAddress>
      <addressBlock>
        <offset>0x0</offset>
        <size>0x200</size>
        <usage>registers</usage>
      </addressBlock>
      <registers>
        <register>
          <name>C</name>
          <description>Control</description>
          <addressOffset>0x00</addressOffset>
          <size>32</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>I2CEN</name>
              <description>I2C Enable</description>
              <bitOffset>15</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>INTR</name>
              <description>Interrupt on RX</description>
              <bitOffset>10</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>INTT</name>
              <description>Interrupt on TX</description>
              <bitOffset>9</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>INTD</name>
              <description>Interrupt on done</description>
              <bitOffset>8</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>ST</name>
              <description>Start transfer</description>
              <bitOffset>7</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CLEAR</name>
              <description>Clear the FIFO</description>
              <bitOffset>4</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>READ</name>
              <description>Transfer is read</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>S</name>
          <description>Status</description>
          <addressOffset>0x04</addressOffset>
          <size>32</size>
          <access>read-write</access>
          <resetValue>0x00000050</resetValue>
          <fields>
            <field>
              <name>CLKT</name>
              <description>Clock stretch timeout</description>
              <bitOffset>9</bitOffset>
              <bitWidth>1</bitWidth>
              <modifiedWriteValues>oneToClear</modifiedWriteValues>
            </field>
            <field>
              <name>ERR</name>
              <description>Error: No ack</description>
              <bitOffset>8</bitOffset>
              <bitWidth>1</bitWidth>
              <modifiedWriteValues>oneToClear</modifiedWriteValues>
            </field>
            <field>
              <name>RXF</name>
              <description>FIFO is full. Can't receive anything else</description>
              <bitOffset>7</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-only</access>
            </field>
            <field>
              <name>TXE</name>
              <description>FIFO is empty. Nothing to transmit</description>
              <bitOffset>6</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-only</access>
            </field>
            <field>
              <name>RXD</name>
              <description>FIFO contains at least one byte</description>
              <bitOffset>5</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-only</access>
            </field>
            <field>
              <name>TXD</name>
              <description>FIFO has space for at least one byte</description>
              <bitOffset>4</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-only</access>
            </field>
            <field>
              <name>RXR</name>
              <description>FIFO needs to be read</description>
              <bitOffset>3</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-only</access>
            </field>
            <field>
              <name>TXW</name>
              <description>FIFO needs to be written</description>
              <bitOffset>2</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-only</access>
            </field>
            <field>
              <name>DONE</name>
              <description>Transfer done</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
              <modifiedWriteValues>oneToClear</modifiedWriteValues>
            </field>
            <field>
              <name>TA</name>
              <description>Transfer active</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-only</access>
            </field>
          </fields>
        </register>
        <register>
          <name>DLEN</name>
          <description>Data length</description>
          <addressOffset>0x08</addressOffset>
          <size>32</size>
          <access>read-only</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>DLEN</name>
              <description>Data length</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>A</name>
          <description>Slave address</description>
          <addressOffset>0x0c</addressOffset>
          <size>32</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>ADDR</name>
              <description>Slave address</description>
              <bitOffset>0</bitOffset>
              <bitWidth>7</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>FIFO</name>
          <description>Data FIFO</description>
          <addressOffset>0x10</addressOffset>
          <size>32</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>DATA</name>
              <description>Access the FIFO</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>DIV</name>
          <description>Clock divider</description>
          <addressOffset>0x14</addressOffset>
          <size>32</size>
          <access>read-write</access>
          <resetValue>0x000005DC</resetValue>
          <fields>
            <field>
              <name>CDIV</name>
              <description>Divide the source clock</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>DEL</name>
          <description>Data delay (Values must be under CDIV / 2)</description>
          <addressOffset>0x18</addressOffset>
          <size>32</size>
          <access>read-write</access>
          <resetValue>0x00300030</resetValue>
          <fields>
            <field>
              <name>FEDL</name>
              <description>Delay before reading after a falling edge</description>
              <bitOffset>16</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
            <field>
              <name>REDL</name>
              <description>Delay before reading after a rising edge</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CLKT</name>
          <description>Clock stretch timeout (broken on 283x)</description>
          <addressOffset>0x1C</addressOffset>
          <size>32</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>TOUT</name>
              <description>Number of SCL clock cycles to wait</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
      </registers>
    </peripheral>
{% endmacro %}
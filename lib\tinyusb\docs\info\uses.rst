****
Uses
****

TinyUSB is currently used by these other projects:

-  `Adafruit nRF52 Arduino <https://github.com/adafruit/Adafruit_nRF52_Arduino>`__
-  `Adafruit nRF52 Bootloader <https://github.com/adafruit/Adafruit_nRF52_Bootloader>`__
-  `Adafruit SAMD Arduino <https://github.com/adafruit/ArduinoCore-samd>`__
-  `CircuitPython <https://github.com/adafruit/circuitpython>`__
-  `Espressif IDF <https://github.com/espressif/esp-idf>`__
-  `MicroPython <https://github.com/micropython/micropython>`__
-  `mynewt <https://mynewt.apache.org>`__
-  `openinput <https://github.com/openinput-fw/openinput>`__
-  `Raspberry Pi Pico SDK <https://github.com/raspberrypi/pico-sdk>`__
-  `TinyUF2 Bootloader <https://github.com/adafruit/tinyuf2>`__
-  `TinyUSB Arduino Library <https://github.com/adafruit/Adafruit_TinyUSB_Arduino>`__

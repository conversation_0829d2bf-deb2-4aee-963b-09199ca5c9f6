name: Build MSP430

on:
  pull_request:
  push:
  release:
    types:
      - created

jobs:  
  build-msp430:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        family:
        # Alphabetical order
        - 'msp430'
    steps:
    - name: Setup Python
      uses: actions/setup-python@v2

    - name: Checkout TinyUSB
      uses: actions/checkout@v2

    - name: Checkout common submodules in lib
      run: git submodule update --init lib/FreeRTOS-Kernel lib/lwip

    - name: Checkout hathach/linkermap
      uses: actions/checkout@v2
      with:
         repository: hathach/linkermap
         path: linkermap

    - name: Set Toolchain URL
      run: echo >> $GITHUB_ENV TOOLCHAIN_URL=http://software-dl.ti.com/msp430/msp430_public_sw/mcu/msp430/MSPGCC/9_2_0_0/export/msp430-gcc-9.2.0.50_linux64.tar.bz2

    - name: <PERSON><PERSON>lchain
      uses: actions/cache@v2
      id: cache-toolchain
      with:
        path: ~/cache/
        key: ${{ runner.os }}-21-03-04-${{ env.TOOLCHAIN_URL }}

    - name: Install Toolchain
      if: steps.cache-toolchain.outputs.cache-hit != 'true'
      run: |
        mkdir -p ~/cache/toolchain
        wget --progress=dot:mega $TOOLCHAIN_URL -O toolchain.tar.bz2
        tar -C ~/cache/toolchain -xaf toolchain.tar.bz2

    - name: Set Toolchain Path
      run: echo >> $GITHUB_PATH `echo ~/cache/toolchain/*/bin`

    - name: Build
      run: python3 tools/build_family.py ${{ matrix.family }}

    - name: Linker Map
      run: |
        pip install linkermap/
        for ex in `ls -d examples/device/*/`; do \
          find ${ex} -name *.map -print -quit | \
          xargs -I % sh -c 'echo "::group::%"; linkermap -v %; echo "::endgroup::"'; \
        done

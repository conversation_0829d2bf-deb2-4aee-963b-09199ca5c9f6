###########################################
# Simple Makefile for HIDAPI test program
#
# Alan <PERSON>
# Signal 11 Software
# 2010-06-01
###########################################

all: hidtest libs

libs: libhidapi.so

CC       ?= cc
CFLAGS   ?= -Wall -g -fPIC

CXX      ?= c++
CXXFLAGS ?= -Wall -g

COBJS     = hid.o
CPPOBJS   = ../hidtest/hidtest.o
OBJS      = $(COBJS) $(CPPOBJS)
INCLUDES  = -I../hidapi -I/usr/local/include
LDFLAGS   = -L/usr/local/lib
LIBS      = -lusb -liconv -pthread


# Console Test Program
hidtest: $(OBJS)
	$(CXX) $(CXXFLAGS) $(LDFLAGS) $^ -o $@ $(LIBS)

# Shared Libs
libhidapi.so: $(COBJS)
	$(CC) $(LDFLAGS) -shared -Wl,-soname,$@.0 $^ -o $@ $(LIBS)

# Objects
$(COBJS): %.o: %.c
	$(CC) $(CFLAGS) -c $(INCLUDES) $< -o $@

$(CPPOBJS): %.o: %.cpp
	$(CXX) $(CXXFLAGS) -c $(INCLUDES) $< -o $@


clean:
	rm -f $(OBJS) hidtest libhidapi.so ../hidtest/hidtest.o

.PHONY: clean libs

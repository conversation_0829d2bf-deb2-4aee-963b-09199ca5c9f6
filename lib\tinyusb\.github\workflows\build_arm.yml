name: Build ARM

on:
  pull_request:
  push:
  release:
    types:
      - created

jobs:
  # ---------------------------------------
  # Unit testing with Ceedling
  # ---------------------------------------
  unit-test:
    runs-on: ubuntu-latest
    steps:
    - name: Setup Ruby
      uses: actions/setup-ruby@v1
      with:
        ruby-version: '2.7'

    - name: Checkout TinyUSB
      uses: actions/checkout@v2

    - name: Unit Tests
      run: |
        # Install Ceedling
        gem install ceedling
        cd test
        ceedling test:all

  # ---------------------------------------
  # Build ARM family
  # ---------------------------------------
  build-arm:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        family:
        # Alphabetical order
        - 'imxrt'
        - 'lpc15'
        - 'lpc18'
        - 'lpc54'
        - 'lpc55'
        - 'mm32'
        - 'msp432e4'
        - 'nrf'
        - 'rp2040'
        - 'samd11'
        - 'samd21'
        - 'samd51'
        - 'saml2x'
        - 'stm32f0'
        - 'stm32f1'
        - 'stm32f4'
        - 'stm32f7'
        - 'stm32h7'
        - 'stm32l4'
        - 'tm4c123'
        - 'xmc4000'
    steps:
    - name: Setup Python
      uses: actions/setup-python@v2

    - name: Checkout TinyUSB
      uses: actions/checkout@v2

    - name: Checkout common submodules in lib
      run: git submodule update --init lib/FreeRTOS-Kernel lib/lwip lib/sct_neopixel

    - name: Checkout hathach/linkermap
      uses: actions/checkout@v2
      with:
         repository: hathach/linkermap
         path: linkermap

    - name: Checkout pico-sdk
      if: matrix.family == 'rp2040'
      run: |
        git clone --depth 1 -b develop https://github.com/raspberrypi/pico-sdk ~/pico-sdk
        echo >> $GITHUB_ENV PICO_SDK_PATH=~/pico-sdk

    - name: Set Toolchain URL
      run: echo >> $GITHUB_ENV TOOLCHAIN_URL=https://github.com/xpack-dev-tools/arm-none-eabi-gcc-xpack/releases/download/v10.2.1-1.1/xpack-arm-none-eabi-gcc-10.2.1-1.1-linux-x64.tar.gz

    - name: Cache Toolchain
      uses: actions/cache@v2
      id: cache-toolchain
      with:
        path: ~/cache/
        key: ${{ runner.os }}-21-03-04-${{ env.TOOLCHAIN_URL }}

    - name: Install Toolchain
      if: steps.cache-toolchain.outputs.cache-hit != 'true'
      run: |
        mkdir -p ~/cache/toolchain
        wget --progress=dot:mega $TOOLCHAIN_URL -O toolchain.tar.gz
        tar -C ~/cache/toolchain -xaf toolchain.tar.gz

    - name: Set Toolchain Path
      run: echo >> $GITHUB_PATH `echo ~/cache/toolchain/*/bin`

    - name: Build
      run: python3 tools/build_family.py ${{ matrix.family }}

    - name: Linker Map
      run: |
        pip install linkermap/
        for ex in `ls -d examples/device/*/`; do \
          find ${ex} -name *.map -print -quit | \
          xargs -I % sh -c 'echo "::group::%"; linkermap -v %; echo "::endgroup::"'; \
        done

  # ---------------------------------------
  # Build all no-family (opharned) boards
  # ---------------------------------------
  build-board:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        example:
        # Alphabetical order, a group of 4
        - 'device/audio_test device/board_test device/cdc_dual_ports device/cdc_msc'
        - 'device/cdc_msc_freertos device/dfu_runtime device/hid_composite device/hid_composite_freertos'
        - 'device/hid_generic_inout device/hid_multiple_interface device/midi_test device/msc_dual_lun'
        - 'device/net_lwip_webserver'
        - 'device/uac2_headset device/usbtmc device/webusb_serial host/cdc_msc_hid'

    steps:
    - name: Setup Python
      uses: actions/setup-python@v2

    - name: Checkout TinyUSB
      uses: actions/checkout@v2

    - name: Checkout common submodules in lib
      run: git submodule update --init lib/FreeRTOS-Kernel lib/lwip

    - name: Set Toolchain URL
      run: echo >> $GITHUB_ENV TOOLCHAIN_URL=https://github.com/xpack-dev-tools/arm-none-eabi-gcc-xpack/releases/download/v10.2.1-1.1/xpack-arm-none-eabi-gcc-10.2.1-1.1-linux-x64.tar.gz

    - name: Cache Toolchain
      uses: actions/cache@v2
      id: cache-toolchain
      with:
        path: ~/cache/
        key: ${{ runner.os }}-21-03-04-${{ env.TOOLCHAIN_URL }}

    - name: Install Toolchain
      if: steps.cache-toolchain.outputs.cache-hit != 'true'
      run: |
        mkdir -p ~/cache/toolchain
        wget --progress=dot:mega $TOOLCHAIN_URL -O toolchain.tar.gz
        tar -C ~/cache/toolchain -xaf toolchain.tar.gz

    - name: Set Toolchain Path
      run: echo >> $GITHUB_PATH `echo ~/cache/toolchain/*/bin`

    - name: Build
      run: python3 tools/build_board.py ${{ matrix.example }}
